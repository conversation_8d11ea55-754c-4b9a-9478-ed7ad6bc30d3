
import React from "react";
import { Card, CardContent } from "@/components/ui/card";

type TestimonialCardProps = {
  quote: string;
  author: string;
  role: string;
  subscribers: string;
};

const TestimonialCard = ({ quote, author, role, subscribers }: TestimonialCardProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          <div className="text-sm text-muted-foreground">
            ★★★★★
          </div>
          <p className="text-base md:text-lg italic">"{quote}"</p>
          <div className="mt-4">
            <p className="font-semibold">{author}</p>
            <p className="text-sm text-muted-foreground">{role}</p>
            <p className="text-xs text-primary">{subscribers}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TestimonialCard;
