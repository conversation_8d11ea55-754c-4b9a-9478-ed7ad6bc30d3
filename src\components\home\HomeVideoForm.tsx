import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Upload, Link as LinkIcon, ArrowRight, Video, RefreshCw, FileUp } from "lucide-react";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { VideoProvider, useVideo } from "@/contexts/VideoContext";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// YouTube niches
const youtubeNiches = [
  "Gaming",
  "Beauty & Fashion",
  "Technology & Gadgets",
  "Travel & Lifestyle",
  "Food & Cooking",
  "Fitness & Health",
  "Education & Learning",
  "Entertainment",
  "Music",
  "DIY & Crafts",
  "Business & Finance",
  "Sports",
  "News & Politics",
  "Comedy",
  "Automotive",
  "Pets & Animals",
  "Science & Technology",
  "Art & Design",
  "Parenting & Family",
  "Other"
];

// Form schema
const formSchema = z.object({
  potentialTitle: z.string().min(1, "Title is required").max(100, "Title is too long"),
  description: z.string().min(1, "Description is required").max(1000, "Description is too long"),
  niche: z.string().min(1, "Please select a niche"),
});

type FormValues = z.infer<typeof formSchema>;

const HomeVideoFormContent = () => {
  const navigate = useNavigate();
  const { videoData, setVideoData } = useVideo();
  const [file, setFile] = useState<File | null>(null);
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [activeTab, setActiveTab] = useState("upload");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      potentialTitle: "",
      description: "",
      niche: "",
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      
      // Check if file is a video
      if (!selectedFile.type.startsWith("video/")) {
        toast({
          variant: "destructive",
          title: "Invalid file type",
          description: "Please upload a video file.",
        });
        return;
      }
      
      // Check if file size is under 100MB
      if (selectedFile.size > 100 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "File too large",
          description: "Please upload a video under 100MB.",
        });
        return;
      }
      
      setFile(selectedFile);
    }
  };

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    
    try {
      if (!file && !youtubeUrl) {
        toast({
          variant: "destructive",
          title: "No video selected",
          description: "Please upload a video file or provide a YouTube URL.",
        });
        setIsSubmitting(false);
        return;
      }

      // Set video data in context
      setVideoData({
        type: file ? 'file' : 'url',
        data: file || youtubeUrl,
        potentialTitle: values.potentialTitle,
        description: values.description,
        niche: values.niche,
      });

      // Show a success message
      toast({
        title: "Success!",
        description: "Your video has been processed. Taking you to AI features!",
      });

      // Navigate to the AI Features page with video data
      setTimeout(() => {
        navigate("/ai-features", {
          state: {
            videoData: {
              type: file ? 'file' : 'url',
              data: file || youtubeUrl,
              potentialTitle: values.potentialTitle,
              description: values.description,
              niche: values.niche,
            }
          }
        });
      }, 1000);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Submission failed",
        description: "There was an error submitting your video details. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
        {/* Horizontal layout with upload on left, fields on right */}
        <div className="grid md:grid-cols-2 gap-5">
          {/* Left side: Video upload options */}
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
            <h3 className="text-sm font-medium mb-3 flex items-center gap-1 text-brand-purple">
              <Video className="h-4 w-4" />
              Video Source
            </h3>
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-2 mb-3 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                <TabsTrigger value="upload" className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
                  <FileUp className="h-3 w-3 mr-1" /> File Upload
                </TabsTrigger>
                <TabsTrigger value="youtube" className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
                  <LinkIcon className="h-3 w-3 mr-1" /> YouTube URL
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="upload">
                <div className="space-y-3">
                  <div 
                    className="border border-dashed border-brand-purple/20 rounded-lg p-4 text-center cursor-pointer hover:bg-brand-purple/5 transition-colors flex items-center justify-center" 
                    onClick={() => document.getElementById("home-video-upload")?.click()}
                    style={{ minHeight: "150px" }}
                  >
                    <div className="flex flex-col items-center">
                      <div className="h-12 w-12 rounded-full bg-brand-purple/10 flex items-center justify-center mb-2">
                        <Upload className="h-6 w-6 text-brand-purple" />
                      </div>
                      <p className="text-sm font-medium">Upload your video</p>
                      <p className="text-xs text-muted-foreground mt-1">MP4, MOV, WebM • Max 100MB</p>
                      <Input id="home-video-upload" type="file" accept="video/*" className="hidden" onChange={handleFileChange} />
                    </div>
                  </div>
                  
                  {file && (
                    <div className="p-3 bg-brand-purple/10 rounded-lg flex justify-between items-center border border-brand-purple/20">
                      <div className="truncate flex items-center gap-2">
                        <Video className="h-4 w-4 text-brand-purple flex-shrink-0" />
                        <div>
                          <p className="font-medium text-sm truncate">{file.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {(file.size / (1024 * 1024)).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <Button 
                        type="button"
                        variant="outline" 
                        size="sm"
                        onClick={() => setFile(null)}
                        className="border-brand-purple/20 hover:bg-brand-purple/10"
                      >
                        Remove
                      </Button>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="youtube">
                <div 
                  className="border border-dashed border-brand-purple/20 rounded-lg p-4 space-y-3"
                  style={{ minHeight: "150px" }}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <LinkIcon className="h-4 w-4 text-brand-purple" />
                    <h4 className="text-sm font-medium">YouTube Video URL</h4>
                  </div>
                  <Input 
                    id="home-youtube-url" 
                    placeholder="https://www.youtube.com/watch?v=..." 
                    value={youtubeUrl}
                    onChange={(e) => setYoutubeUrl(e.target.value)}
                    className="border-brand-purple/20 focus-visible:ring-brand-purple/30"
                  />
                  <p className="text-xs text-muted-foreground ml-1">
                    Enter a public YouTube video URL to analyze
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right side: Title and Niche fields stacked vertically */}
          <div className="space-y-4">
            <div>
              <FormField
                control={form.control}
                name="potentialTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium flex items-center gap-1">
                      <span className="h-4 w-4 bg-brand-purple/10 rounded-full flex items-center justify-center text-xs text-brand-purple font-bold">1</span>
                      Video Title
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter a title for your video" 
                        {...field}
                        className="border-brand-purple/20 focus-visible:ring-brand-purple/30"
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      This helps our AI understand your content
                    </FormDescription>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
            
            <div>
              <FormField
                control={form.control}
                name="niche"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium flex items-center gap-1">
                      <span className="h-4 w-4 bg-brand-purple/10 rounded-full flex items-center justify-center text-xs text-brand-purple font-bold">2</span>
                      Content Niche
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="border-brand-purple/20 focus:ring-brand-purple/30">
                          <SelectValue placeholder="Select your video niche" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {youtubeNiches.map((niche) => (
                          <SelectItem key={niche} value={niche}>
                            {niche}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription className="text-xs">
                      Select the category that best fits your video
                    </FormDescription>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>
        
        {/* Bottom: Description field spanning full width */}
        <div className="pt-2 border-t border-gray-100 dark:border-gray-800">
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium flex items-center gap-1">
                  <span className="h-4 w-4 bg-brand-purple/10 rounded-full flex items-center justify-center text-xs text-brand-purple font-bold">3</span>
                  Video Description
                </FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Briefly describe your video content" 
                    className="min-h-[80px] border-brand-purple/20 focus-visible:ring-brand-purple/30" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription className="text-xs">
                  Provide details about your content to get better AI recommendations
                </FormDescription>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </div>

        <Button 
          type="submit" 
          className="w-full bg-gradient-to-r from-brand-purple to-brand-blue hover:opacity-90 transition-opacity py-2 font-medium"
          disabled={isSubmitting || (!file && !youtubeUrl)}
        >
          {isSubmitting ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              Get AI Recommendations
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </form>
    </Form>
  );
};

// Wrap the component with VideoProvider
const HomeVideoForm = () => (
  <VideoProvider>
    <HomeVideoFormContent />
  </VideoProvider>
);

export default HomeVideoForm;
