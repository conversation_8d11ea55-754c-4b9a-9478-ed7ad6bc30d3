import React from "react";
import RealAnalyticsSummary from "../analytics/RealAnalyticsSummary";
import VideoIdeasSection from "./VideoIdeasSection";
import TrendingTopicsSection from "./TrendingTopicsSection";
import RecentVideosSection from "./RecentVideosSection";
import { Button } from "@/components/ui/button";
import { PlusCircle, BarChart3 } from "lucide-react";
import { useNavigate } from "react-router-dom";

const DashboardOverview = () => {
  const navigate = useNavigate();
  
  return (
    <div className="space-y-6">
      {/* Welcome Section with Action Buttons */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold">Welcome back!</h1>
          <p className="text-muted-foreground">Here's what's happening with your YouTube channel</p>
        </div>
        <div className="flex gap-3">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => navigate("/analytics")}
          >
            <BarChart3 className="h-4 w-4" />
            Full Analytics
          </Button>
          <Button 
            className="bg-gradient-to-r from-brand-purple to-brand-blue hover:opacity-90 flex items-center gap-2"
            onClick={() => navigate("/new-video")}
          >
            <PlusCircle className="h-4 w-4" />
            Create New Video
          </Button>
        </div>
      </div>
        {/* Analytics Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Channel Analytics</h2>
        <RealAnalyticsSummary />
      </div>
      
      {/* Layout Grid for Bottom Sections */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Video Ideas Section */}
        <div>
          <VideoIdeasSection />
        </div>
        
        {/* Trending Topics */}
        <div>
          <TrendingTopicsSection />
        </div>
      </div>
      
      {/* Recent Videos */}
      <div className="mt-6">
        <RecentVideosSection />
      </div>
    </div>
  );
};

export default DashboardOverview;
