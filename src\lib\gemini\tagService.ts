import { API_CONFIG } from '../config';
import { toast } from '../../components/ui/use-toast';
import { callGeminiApi } from './client';

export interface TagGenerationRequest {
  title: string;
  description: string;
  competitorUrl?: string;
  specificKeywords?: string;
  tagCount?: string;
  includeNicheTags?: boolean;
  includeBrandTags?: boolean;
  localLanguage?: string;
  niche?: string;
}

export interface TagGenerationResponse {
  tags: string[];
}

/**
 * Constructs a prompt for tag generation based on video details and preferences
 */
function constructTagGenerationPrompt(data: TagGenerationRequest): string {
  const { 
    title, 
    description, 
    competitorUrl, 
    specificKeywords, 
    tagCount = "15", 
    includeNicheTags = true, 
    includeBrandTags = true,
    localLanguage = "english",
    niche
  } = data;
  
  // Convert tagCount to number (default to 15 if conversion fails)
  const numTags = parseInt(tagCount, 10) || 15;
  
  return `
You are a YouTube tag optimization expert. Your task is to create SEO-friendly, high-performing tags for a YouTube video.

VIDEO DETAILS:
Title: ${title}
${description ? `Description: ${description}` : ''}
${niche ? `Niche: ${niche}` : ''}
${competitorUrl ? `Competitor Video URL: ${competitorUrl}` : ''}
${specificKeywords ? `Specific Keywords to Include: ${specificKeywords}` : ''}

TAG PREFERENCES:
- Number of tags to generate: ${numTags}
- Include niche-specific tags: ${includeNicheTags ? 'Yes' : 'No'}
- Include brand-related tags: ${includeBrandTags ? 'Yes' : 'No'}
- Language: ${localLanguage}

Generate ${numTags} YouTube tags that:
1. Are relevant to the video content and will help with search ranking
2. Include a mix of broad, medium, and specific tags for maximum discoverability
3. Are optimized for the YouTube algorithm
4. Are formatted correctly for YouTube (no # symbols, commas, or special characters)
5. Each tag is between 1-3 words (occasionally longer for specific phrases)
${includeNicheTags ? '6. Include several niche-specific tags that are trending in this content area' : ''}
${includeBrandTags ? '7. Include 1-2 brand-related tags if applicable based on the content' : ''}

Format your response ONLY as a JSON array of strings containing the generated tags:
["tag1", "tag2", "tag3", ...]

Do not include any explanation or other text outside the JSON array.
`.trim();
}

/**
 * Parses the generated response to extract tags
 */
function parseGeneratedTags(text: string): string[] {
  try {
    // Try to extract JSON array if the response is properly formatted
    const jsonMatch = text.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const jsonStr = jsonMatch[0];
      const tags = JSON.parse(jsonStr);
      if (Array.isArray(tags)) {
        return tags.filter(tag => typeof tag === 'string' && tag.trim().length > 0);
      }
    }
  } catch (e) {
    console.error("Failed to parse JSON from API response:", e);
  }

  // Fallback: Extract each line as a tag if JSON parsing fails
  try {
    const lines = text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && !line.startsWith('[') && !line.startsWith(']') && !line.startsWith('{') && !line.startsWith('}'));
    
    // Extract quoted strings or plain text
    const extractedTags: string[] = [];
    for (const line of lines) {
      // Try to extract quoted strings
      const matches = line.match(/"([^"]+)"/g);
      if (matches && matches.length > 0) {
        matches.forEach(match => {
          const tag = match.replace(/"/g, '').trim();
          if (tag) extractedTags.push(tag);
        });
      } else {
        // If no quotes, clean up the line
        const cleanLine = line
          .replace(/^[\d\s."',[\]:]+/, '') // Remove leading numbers, spaces, quotes, commas, etc.
          .replace(/["',[\]]+$/, '')       // Remove trailing quotes, commas, etc.
          .trim();
          
        if (cleanLine && !cleanLine.includes(':') && !cleanLine.includes('```')) {
          extractedTags.push(cleanLine);
        }
      }
    }
    
    return extractedTags;
  } catch (e) {
    console.error("Fallback parsing of tags failed:", e);
    return [];
  }
}

/**
 * Generates optimized tags for a YouTube video using the Gemini API
 */
export async function generateTags(
  data: TagGenerationRequest
): Promise<TagGenerationResponse | null> {
  try {
    const apiKey = API_CONFIG.gemini.apiKey;
    
    if (!apiKey) {
      toast({
        variant: "destructive",
        title: "Gemini API Key Not Found",
        description: "Please add your Gemini API key to the .env.local file (VITE_GEMINI_API_KEY)."
      });
      console.error("Gemini API key is missing. Please check your .env.local file.");
      return null;
    }

    if (!data.title || data.title.trim() === "") {
      toast({
        variant: "destructive",
        title: "Missing Video Title",
        description: "Video title is required for tag generation."
      });
      return null;
    }
      const prompt = constructTagGenerationPrompt(data);
    
    console.log("Sending tag generation request to Gemini API...");    // Use gemini-1.5-flash model which is more widely supported
    const response = await callGeminiApi(
      prompt, 
      apiKey,
      "gemini-1.5-flash", // Use the same model as other services
      {
        temperature: 0.2, // Lower temperature for more consistent tags
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 1024,
      }
    );

    if (!response) {
      throw new Error("No response from Gemini API");
    }

    console.log("Tag generation successful. Processing response...");
    
    const parsedTags = parseGeneratedTags(response);
    
    // Filter out any empty tags or tags that are too long for YouTube
    const validTags = parsedTags
      .filter(tag => tag && tag.length > 0 && tag.length <= 30)
      .map(tag => tag.trim());
    
    if (validTags.length === 0) {
      throw new Error("No valid tags generated from the API response");
    }

    return {
      tags: validTags,
    };  } catch (error: any) {
    console.error("Error generating tags:", error);
    
    // Format a user-friendly error message
    let errorMessage = "Failed to generate tags. Please try again.";
    
    if (error.message) {
      if (error.message.includes("404") || error.message.includes("NOT_FOUND")) {
        errorMessage = "Model not found. We're working to resolve this issue.";
      } else if (error.message.includes("429") || error.message.includes("RESOURCE_EXHAUSTED")) {
        errorMessage = "API rate limit reached. Please try again in a few minutes.";
      } else if (error.message.includes("403") || error.message.includes("401") || 
                error.message.includes("PERMISSION_DENIED") || error.message.includes("UNAUTHENTICATED")) {
        errorMessage = "API key authentication error. Please check your configuration.";
      } else {
        // Use the original error message if it's informative
        errorMessage = error.message;
      }
    }
    
    toast({
      variant: "destructive",
      title: "Tag Generation Failed",
      description: errorMessage
    });
    return null;
  }
}
