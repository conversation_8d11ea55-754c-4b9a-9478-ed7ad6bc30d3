-- Create tables for storing YouTube analytics data and user channel connections

-- Table to store user's connected YouTube channels
CREATE TABLE IF NOT EXISTS youtube_channels (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    channel_id TEXT NOT NULL,
    channel_title TEXT,
    channel_description TEXT,
    subscriber_count INTEGER DEFAULT 0,
    video_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    access_token TEXT, -- Encrypted access token for API calls
    refresh_token TEXT, -- Encrypted refresh token
    token_expires_at TIMESTAMP WITH TIME ZONE,
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, channel_id)
);

-- Table to store daily analytics snapshots
CREATE TABLE IF NOT EXISTS channel_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    channel_id UUID REFERENCES youtube_channels(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    views INTEGER DEFAULT 0,
    watch_time_minutes INTEGER DEFAULT 0,
    subscribers_gained INTEGER DEFAULT 0,
    subscribers_lost INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    comments INTEGER DEFAULT 0,
    shares INTEGER DEFAULT 0,
    estimated_revenue DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(channel_id, date)
);

-- Table to store individual video analytics
CREATE TABLE IF NOT EXISTS video_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    channel_id UUID REFERENCES youtube_channels(id) ON DELETE CASCADE,
    video_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    thumbnail_url TEXT,
    views INTEGER DEFAULT 0,
    watch_time_minutes INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    dislikes INTEGER DEFAULT 0,
    comments INTEGER DEFAULT 0,
    shares INTEGER DEFAULT 0,
    click_through_rate DECIMAL(5,2) DEFAULT 0,
    average_view_duration_seconds INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(channel_id, video_id)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_youtube_channels_user_id ON youtube_channels(user_id);
CREATE INDEX IF NOT EXISTS idx_channel_analytics_channel_date ON channel_analytics(channel_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_video_analytics_channel_id ON video_analytics(channel_id);
CREATE INDEX IF NOT EXISTS idx_video_analytics_published_at ON video_analytics(published_at DESC);

-- Enable Row Level Security (RLS)
ALTER TABLE youtube_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE channel_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own YouTube channels
CREATE POLICY "Users can view own channels" ON youtube_channels
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own channels" ON youtube_channels
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own channels" ON youtube_channels
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own channels" ON youtube_channels
    FOR DELETE USING (auth.uid() = user_id);

-- Users can only see analytics for their own channels
CREATE POLICY "Users can view own channel analytics" ON channel_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM youtube_channels 
            WHERE youtube_channels.id = channel_analytics.channel_id 
            AND youtube_channels.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own channel analytics" ON channel_analytics
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM youtube_channels 
            WHERE youtube_channels.id = channel_analytics.channel_id 
            AND youtube_channels.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own channel analytics" ON channel_analytics
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM youtube_channels 
            WHERE youtube_channels.id = channel_analytics.channel_id 
            AND youtube_channels.user_id = auth.uid()
        )
    );

-- Users can only see video analytics for their own channels
CREATE POLICY "Users can view own video analytics" ON video_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM youtube_channels 
            WHERE youtube_channels.id = video_analytics.channel_id 
            AND youtube_channels.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own video analytics" ON video_analytics
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM youtube_channels 
            WHERE youtube_channels.id = video_analytics.channel_id 
            AND youtube_channels.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own video analytics" ON video_analytics
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM youtube_channels 
            WHERE youtube_channels.id = video_analytics.channel_id 
            AND youtube_channels.user_id = auth.uid()
        )
    );
