import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { LightbulbIcon, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

// Mock data for video ideas
const videoIdeasData = [
  {
    id: 1,
    title: "10 Essential Tips for Growing Your YouTube Channel in 2025",
    category: "Growth Strategy",
    estimatedViews: "10K-15K",
  },
  {
    id: 2,
    title: "The Latest AI Tools Every Content Creator Needs",
    category: "Technology",
    estimatedViews: "8K-12K",
  },
  {
    id: 3,
    title: "Budget Home Studio Setup for Professional Looking Videos",
    category: "Equipment",
    estimatedViews: "5K-9K",
  }
];

const VideoIdeasSection = () => {
  return (
    <Card className="h-full">
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <LightbulbIcon className="h-5 w-5 text-yellow-500" />
          <CardTitle className="text-lg font-semibold">Video Ideas</CardTitle>
        </div>
        <Button variant="ghost" size="sm" className="text-xs text-muted-foreground">
          See all
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {videoIdeasData.map((idea) => (
            <VideoIdeaCard key={idea.id} idea={idea} />
          ))}

          <Button variant="outline" size="sm" className="w-full mt-2 text-brand-purple border-brand-purple/20">
            Generate More Ideas <ArrowRight className="ml-2 h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

interface VideoIdeaCardProps {
  idea: {
    id: number;
    title: string;
    category: string;
    estimatedViews: string;
  };
}

const VideoIdeaCard = ({ idea }: VideoIdeaCardProps) => {
  return (
    <div className="p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors cursor-pointer">
      <h4 className="font-medium text-sm mb-1">{idea.title}</h4>
      <div className="flex justify-between items-center mt-2">
        <span className="bg-brand-purple/10 text-brand-purple text-xs px-2 py-0.5 rounded">
          {idea.category}
        </span>
        <span className="text-xs text-muted-foreground">
          Est. Views: {idea.estimatedViews}
        </span>
      </div>
    </div>
  );
};

export default VideoIdeasSection;
