# API Keys Setup Guide

This guide explains how to set up and manage the API keys required for VideoGrowth AI.

## Environment Variables

VideoGrowth AI uses environment variables to securely store API keys. These are defined in a `.env.local` file in the root of the project.

## Getting Started

1. Copy the `.env.local.example` file to create your own `.env.local` file
2. Fill in your API keys in the new `.env.local` file
3. The application will automatically use these keys

## Google Gemini API

### Key Format and Structure

The application uses two environment variables for Gemini API keys:

- `VITE_GEMINI_API_KEY`: Your primary API key
- `VITE_GEMINI_ALT_API_KEYS`: Alternative API keys for handling rate limits

Important notes:
- Each key should be on its own line or separated with commas
- Do not include spaces in the keys
- Do not surround keys with quotes

### How API Keys Are Used

1. The primary key is used for all initial requests
2. If the primary key hits a rate limit, alternative keys are used
3. Keys are rotated automatically when rate limits are encountered

### Model Format

VideoGrowth AI uses the `gemini-1.5-flash` model which offers the best balance of performance and cost.

### Rate Limiting

Google has the following rate limits for free API keys:
- 60 requests per minute
- 60 requests per day per IP address
- 60 TPM (tokens per minute)

To handle rate limiting:
1. The application implements a queue system for requests
2. Exponential backoff is used when rate limits are encountered
3. Multiple API keys are rotated to increase throughput

## YouTube API

The YouTube API key is used for fetching trending videos and other YouTube data.

## Supabase Configuration

Supabase URL and anonymous key are required for authentication features.

## Troubleshooting

### 404 Errors
If you see 404 errors, check that you're using the correct model name format.

### 429 Errors
429 errors indicate rate limiting. The application will automatically:
1. Retry with exponential backoff
2. Switch to alternative API keys if available

### API Key Issues
- Make sure keys are properly formatted (no spaces, commas, or quotes)
- Verify your API keys are active in the Google AI Studio
- Check that you have the correct permissions for the models you're trying to access
