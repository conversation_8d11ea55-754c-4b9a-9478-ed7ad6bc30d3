import { API_CONFIG } from '../config';
import { toast } from '../../components/ui/use-toast'; 
import { callGeminiApi } from './client'; 

export interface DescriptionGenerationRequest {
  videoSummary: string;
  affiliateLinks?: {
    productName: string;
    url: string;
    description?: string;
  }[];
  includeTimestamps?: boolean;
  targetKeywords?: string[];
  callToAction?: string;
  niche?: string;
}

export interface DescriptionGenerationResponse {
  description: string;
}

function constructDescriptionGenerationPrompt(data: DescriptionGenerationRequest): string {
  const { videoSummary, affiliateLinks, includeTimestamps, targetKeywords, callToAction, niche } = data;
  
  let prompt = `
You are a YouTube description optimization expert. Your task is to create an SEO-friendly, engaging description for a YouTube video.

VIDEO SUMMARY:
${videoSummary}

${niche ? `CONTENT NICHE: ${niche}` : ''}
`;

  if (affiliateLinks && affiliateLinks.length > 0) {
    prompt += '\nAFFILIATE LINKS TO INCLUDE:';
    affiliateLinks.forEach((link, index) => {
      prompt += `\n${index + 1}. Product: ${link.productName} | URL: ${link.url}`;
      if (link.description) {
        prompt += ` | Description: ${link.description}`;
      }
    });
  }

  if (includeTimestamps) {
    prompt += '\n\nInclude 3-5 timestamp placeholders for key moments in the video.';
  }

  if (targetKeywords && targetKeywords.length > 0) {
    prompt += `\n\nTARGET KEYWORDS TO INCLUDE: ${targetKeywords.join(', ')}`;
  }

  if (callToAction) {
    prompt += `\n\nINCLUDE THIS CALL TO ACTION: ${callToAction}`;
  }

  prompt += `
  
Create a YouTube description that:
1. Starts with an engaging 2-3 sentence summary of the video content
2. Is formatted with clear sections and proper spacing
3. Includes relevant hashtags for discoverability
4. Incorporates all affiliate links in a clean, organized way
5. Has a strong call-to-action
6. Is optimized for YouTube SEO
7. Is between 100-200 words in total

Format the description exactly as it should appear in YouTube, with proper line breaks and formatting.
`;

  return prompt.trim();
}

// Removed fallback description generation as per requirements

export async function generateDescription(
  data: DescriptionGenerationRequest
): Promise<DescriptionGenerationResponse | null> {
  try {
    const apiKey = API_CONFIG.gemini.apiKey;
    
    if (!apiKey) {
      toast({
        variant: "destructive",
        title: "Gemini API Key Not Found",
        description: "Please add your Gemini API key to the .env.local file (VITE_GEMINI_API_KEY)."
      });
      console.error("Gemini API key is missing. Please check your .env.local file.");
      return null;
    }

    if (!data.videoSummary || data.videoSummary.trim() === "") {
      toast({
        variant: "destructive",
        title: "Missing Video Summary",
        description: "Video summary is required for description generation."
      });
      return null;
    }    
    const prompt = constructDescriptionGenerationPrompt(data);
    console.log("Sending description request to Gemini API...");
    
    try {
      // Use gemini-1.5-flash model with optimized settings
      const options = {
        temperature: 0.6,
        maxOutputTokens: 512
      };
      const generatedText = await callGeminiApi(prompt, apiKey, "gemini-1.5-flash", options);
      console.log("Raw Gemini response for description:", generatedText);
      
      return { description: generatedText };
    } catch (error: any) {
      console.error("Error calling Gemini API for description:", error.message);
      toast({
        variant: "destructive",
        title: "Description Generation Failed",
        description: error.message || "Failed to generate description. Please try again later."
      });
      return null;
    }
  } catch (error: any) {
    console.error(`Description generation error: ${error.message}`);
    toast({
      variant: "destructive",
      title: "Description Generation Failed",
      description: error.message || "Failed to generate description. Please try again."
    });
    return null;
  }
}
