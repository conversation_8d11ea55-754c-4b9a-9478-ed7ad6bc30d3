import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Sidebar from "@/components/dashboard/Sidebar";
import Header from "@/components/dashboard/Header";
import VideoUploader from "@/components/title-generator/VideoUploader";
import { toast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Download, RefreshCw, ArrowLeft } from "lucide-react";
import { generateThumbnails, ThumbnailGenerationRequest } from "@/lib/api";
import { useVideo, VideoProvider } from "@/contexts/VideoContext";

// Inner component to use the useVideo hook
const ThumbnailCreatorContent = () => {
  const { videoData } = useVideo();
  const location = useLocation();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [generatedThumbnails, setGeneratedThumbnails] = useState<string[]>([]);
  const [selectedThumbnail, setSelectedThumbnail] = useState<number | null>(null);

  // Check if video data was passed from the dashboard or context
  useEffect(() => {
    // First check context, then location state
    const videoDataSource = videoData || location.state?.videoData;
    
    if (videoDataSource) {
      processVideo(videoDataSource);
    } else {
      // If no data is available, redirect to AI features page
      toast({
        variant: "destructive",
        title: "No video selected",
        description: "Please select a video first.",
      });
      navigate('/ai-features');
    }
  }, [location.state, videoData, navigate]);

  const processVideo = async (videoData: any) => {
    setIsLoading(true);

    try {
      // Prepare the request data with additional metadata
      const requestData: ThumbnailGenerationRequest = videoData.type === 'url'
        ? {
            youtubeUrl: videoData.data as string,
            title: videoData.potentialTitle,
            customText: videoData.description,
            style: videoData.niche?.toLowerCase().includes('gaming') ? 'clickbait' :
                  videoData.niche?.toLowerCase().includes('education') ? 'professional' :
                  videoData.niche?.toLowerCase().includes('business') ? 'professional' : 'modern'
          }
        : {
            videoFile: videoData.data as File,
            title: videoData.potentialTitle,
            customText: videoData.description,
            style: videoData.niche?.toLowerCase().includes('gaming') ? 'clickbait' :
                  videoData.niche?.toLowerCase().includes('education') ? 'professional' :
                  videoData.niche?.toLowerCase().includes('business') ? 'professional' : 'modern'
          };

      // Call the API function
      const response = await generateThumbnails(requestData);

      if (response.success && response.data) {
        setGeneratedThumbnails(response.data.thumbnails);
        toast({
          title: "Thumbnails Generated!",
          description: "AI has generated thumbnail suggestions based on your video.",
        });
      } else {
        throw new Error(response.error || "Failed to generate thumbnails");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Generation Failed",
        description: error.message || "Failed to generate thumbnails. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleVideoSubmit = async (file: File | string) => {
    setIsLoading(true);

    try {
      // Prepare the request data
      const requestData: ThumbnailGenerationRequest = typeof file === 'string'
        ? { youtubeUrl: file }
        : { videoFile: file };

      // Call the API function
      const response = await generateThumbnails(requestData);

      if (response.success && response.data) {
        setGeneratedThumbnails(response.data.thumbnails);
        toast({
          title: "Thumbnails Generated!",
          description: "AI has generated thumbnail suggestions based on your video.",
        });
      } else {
        throw new Error(response.error || "Failed to generate thumbnails");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Generation Failed",
        description: error.message || "Failed to generate thumbnails. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setGeneratedThumbnails([]);
    setSelectedThumbnail(null);
  };
  
  const handleBack = () => {
    // Pass video data from either context or location state when navigating back
    navigate('/ai-features', { state: { videoData: videoData || location.state?.videoData } });
  };

  const handleThumbnailSelect = (index: number) => {
    setSelectedThumbnail(index);
    toast({
      description: "Thumbnail selected!",
    });
  };

  const handleDownload = (url: string) => {
    // Create a temporary anchor element
    const a = document.createElement('a');
    a.href = url;
    a.download = `thumbnail-${Date.now()}.jpg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    toast({
      description: "Thumbnail downloaded!",
    });
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-muted/30">
      <div className="w-64 hidden lg:block">
        <Sidebar />
      </div>
      <div className="lg:hidden">
        <Sidebar />
      </div>

      <div className="flex-1 flex flex-col lg:pt-0 pt-16">
        <Header
          title="Thumbnail Creator"
          subtitle="Design eye-catching thumbnails for your YouTube videos"
        />

        <main className="flex-1 p-4 md:p-6">
          <div className="max-w-4xl mx-auto">
            {/* Back button */}
            <Button 
              variant="outline"
              size="sm"
              onClick={handleBack}
              className="mb-4 flex items-center gap-1"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            
            {generatedThumbnails.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>AI Generated Thumbnail Suggestions</CardTitle>
                  <CardDescription>
                    Select the thumbnail you like best. Click on a suggestion to select it.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {generatedThumbnails.map((thumbnail, index) => (
                      <div
                        key={index}
                        className={`rounded-lg border overflow-hidden cursor-pointer transition-all ${
                          selectedThumbnail === index ? 'ring-2 ring-primary' : 'hover:opacity-90'
                        }`}
                        onClick={() => handleThumbnailSelect(index)}
                      >
                        <div className="relative aspect-video">
                          <img
                            src={thumbnail}
                            alt={`Thumbnail option ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                          {selectedThumbnail === index && (
                            <div className="absolute top-2 right-2">
                              <Button
                                size="icon"
                                variant="secondary"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDownload(thumbnail);
                                }}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                        <div className="p-3 bg-card">
                          <p className="text-sm font-medium">Thumbnail Option {index + 1}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 flex justify-end">
                    <Button variant="outline" onClick={handleReset}>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Generate More Thumbnails
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="mb-6">
                  <h2 className="text-xl font-semibold mb-2">Create Eye-Catching Thumbnails</h2>
                  <p className="text-muted-foreground">
                    Upload your video or provide a YouTube URL to get AI-generated thumbnail suggestions
                    that increase your click-through rate and views.
                  </p>
                </div>

                <VideoUploader onVideoSubmit={handleVideoSubmit} isLoading={isLoading} />
              </>
            )}
          </div>
        </main>

        <div className="fixed top-4 left-4 z-50">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="rounded-full p-2 shadow-md hover:shadow-lg transition-all"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const ThumbnailCreator = () => {
  return (
    <VideoProvider>
      <ThumbnailCreatorContent />
    </VideoProvider>
  );
};

export default ThumbnailCreator;
