
import React from "react";

const HeroImage = () => {
  return (
    <div className="relative rounded-lg bg-gradient-to-r from-brand-purple/10 to-brand-blue/10 p-4">
      <div className="bg-white rounded-md shadow-2xl overflow-hidden border">
        <div className="h-10 bg-gray-100 border-b flex items-center px-4">
          <div className="flex gap-2">
            <div className="h-3 w-3 rounded-full bg-red-400" />
            <div className="h-3 w-3 rounded-full bg-yellow-400" />
            <div className="h-3 w-3 rounded-full bg-green-400" />
          </div>
        </div>
        
        <div className="p-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-2">
              <div className="h-8 w-full bg-gray-100 rounded mb-4"></div>
              <div className="h-32 w-full bg-gradient-to-r from-brand-purple/20 to-brand-blue/20 rounded mb-4 flex items-center justify-center">
                <div className="h-12 w-12 rounded-full bg-brand-purple/30 flex items-center justify-center">
                  <div className="h-6 w-6 rounded-full bg-brand-purple/60"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-4 w-full bg-gray-100 rounded"></div>
                <div className="h-4 w-5/6 bg-gray-100 rounded"></div>
                <div className="h-4 w-4/6 bg-gray-100 rounded"></div>
              </div>
            </div>
            <div className="col-span-1">
              <div className="h-full bg-gray-50 rounded border p-2">
                <div className="h-4 w-full bg-gray-100 rounded mb-2"></div>
                <div className="h-4 w-4/5 bg-gray-100 rounded mb-2"></div>
                <div className="h-4 w-3/5 bg-gray-100 rounded mb-4"></div>
                <div className="h-8 w-full bg-brand-purple/30 rounded mb-2"></div>
                <div className="h-8 w-full bg-brand-blue/30 rounded mb-2"></div>
                <div className="h-8 w-full bg-brand-pink/30 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="absolute -bottom-6 -right-6 h-20 w-20 bg-gradient-to-br from-brand-purple to-brand-blue rounded-full opacity-30 blur-xl animate-pulse-slow"></div>
      <div className="absolute -top-6 -left-6 h-16 w-16 bg-gradient-to-br from-brand-pink to-brand-purple rounded-full opacity-30 blur-xl animate-pulse-slow"></div>
    </div>
  );
};

export default HeroImage;
