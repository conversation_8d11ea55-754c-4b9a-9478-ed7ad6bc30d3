import React from "react";
import Sidebar from "@/components/dashboard/Sidebar";
import Header from "@/components/dashboard/Header";
import { VideoProvider } from "@/contexts/VideoContext";
import HomeVideoForm from "@/components/home/<USER>";
import { ChevronLeft, Video, Sparkles, Upload, Youtube, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const NewVideo = () => {
  const navigate = useNavigate();
  
  return (
    <VideoProvider>
      <div className="min-h-screen flex flex-col lg:flex-row bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
        {/* Sidebar */}
        <div className="hidden lg:block lg:w-64 lg:flex-shrink-0">
          <Sidebar />
        </div>
        <div className="lg:hidden">
          <Sidebar />
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col lg:pt-0 pt-16 relative">
          {/* Back button - positioned absolutely for better immersion */}
          <div className="absolute top-6 left-6 z-10">
            <Button 
              variant="outline" 
              size="sm"
              className="flex items-center gap-1 bg-white/80 backdrop-blur-sm hover:bg-white/90"
              onClick={() => navigate("/dashboard")}
            >
              <ChevronLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
          </div>
          
          {/* Large header section */}
          <div className="bg-gradient-to-r from-brand-purple to-brand-blue p-12 lg:p-16 text-white">
            <div className="max-w-5xl mx-auto">
              <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
                <div className="space-y-2">
                  <div className="inline-flex items-center gap-3 bg-white/20 backdrop-blur-sm rounded-full px-4 py-1 mb-2">
                    <Video className="h-4 w-4" />
                    <span className="text-sm font-medium">New Project</span>
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold leading-tight">Create Your Next Viral Video</h1>
                  <p className="text-white/80 max-w-lg">
                    Upload your video and let our AI analyze it to provide you with the best recommendations for titles, descriptions, and thumbnails.
                  </p>
                </div>
                <div className="h-20 w-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <Sparkles className="h-10 w-10 text-white" />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center gap-3">
                  <div className="h-10 w-10 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Upload className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">Upload Your Video</h3>
                    <p className="text-sm text-white/70">Upload your video file or provide a YouTube URL</p>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center gap-3">
                  <div className="h-10 w-10 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Youtube className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">Add Video Details</h3>
                    <p className="text-sm text-white/70">Provide information about your content</p>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center gap-3">
                  <div className="h-10 w-10 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Lightbulb className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">Get AI Recommendations</h3>
                    <p className="text-sm text-white/70">Our AI will analyze and optimize your video</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Video form area with glass effect */}
          <main className="flex-1">
            <div className="max-w-5xl mx-auto -mt-10 px-4 mb-10">
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
                <div className="p-8">
                  <HomeVideoForm />
                </div>
              </div>
            </div>
            
            {/* Additional information and guidance */}
            <div className="max-w-5xl mx-auto px-4 mb-10 grid md:grid-cols-2 gap-6">
              <div className="rounded-lg bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold mb-4">Tips for Great Videos</h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-brand-purple/10 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-brand-purple text-xs font-bold">1</span>
                    </div>
                    <span className="text-sm">Keep your video focused on a single topic</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-brand-purple/10 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-brand-purple text-xs font-bold">2</span>
                    </div>
                    <span className="text-sm">Include clear audio and good lighting</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-brand-purple/10 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-brand-purple text-xs font-bold">3</span>
                    </div>
                    <span className="text-sm">Be specific with your video title and description</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-brand-purple/10 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-brand-purple text-xs font-bold">4</span>
                    </div>
                    <span className="text-sm">Choose the right niche to reach your target audience</span>
                  </li>
                </ul>
              </div>
              
              <div className="rounded-lg bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold mb-4">What Happens Next</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  After uploading your video, our AI will analyze it and provide you with:
                </p>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-green-600 dark:text-green-400 text-xs">✓</span>
                    </div>
                    <span className="text-sm">High-CTR title suggestions</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-green-600 dark:text-green-400 text-xs">✓</span>
                    </div>
                    <span className="text-sm">Eye-catching thumbnail designs</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-green-600 dark:text-green-400 text-xs">✓</span>
                    </div>
                    <span className="text-sm">SEO-optimized descriptions</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-green-600 dark:text-green-400 text-xs">✓</span>
                    </div>
                    <span className="text-sm">Targeted tags for better discoverability</span>
                  </li>
                </ul>
              </div>
            </div>
          </main>
        </div>
      </div>
    </VideoProvider>
  );
};

export default NewVideo;
