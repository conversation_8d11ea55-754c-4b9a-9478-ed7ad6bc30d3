import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Video, MoreHorizontal, Eye, ThumbsUp, MessageCircle, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Mock data for recent videos
const recentVideosData = [
  {
    id: "v1",
    title: "How I Grew My YouTube Channel to 10K Subscribers in 3 Months",
    thumbnail: "https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?w=300&h=200&fit=crop&q=80",
    publishDate: "2025-05-05T12:00:00Z",
    views: 8420,
    likes: 743,
    comments: 128,
    duration: "18:42",
    status: "published"
  },
  {
    id: "v2",
    title: "The Best Camera Settings for YouTube Videos in 2025",
    thumbnail: "https://images.unsplash.com/photo-1560169897-fc0cdbdfa4d5?w=300&h=200&fit=crop&q=80",
    publishDate: "2025-04-28T14:30:00Z",
    views: 12650,
    likes: 952,
    comments: 213,
    duration: "12:19",
    status: "published"
  },
  {
    id: "v3",
    title: "10 Tips for Better Video Editing - Beginners Guide",
    thumbnail: "https://images.unsplash.com/photo-1574717024453-354056aad975?w=300&h=200&fit=crop&q=80",
    publishDate: "2025-04-15T10:00:00Z",
    views: 9370,
    likes: 812,
    comments: 156,
    duration: "15:27",
    status: "published"
  },
  {
    id: "v4",
    title: "Setting Up the Perfect YouTube Home Studio",
    thumbnail: "https://images.unsplash.com/photo-1598550476439-6847785fcea6?w=300&h=200&fit=crop&q=80",
    publishDate: "2025-05-10T09:00:00Z",
    views: 0,
    likes: 0,
    comments: 0,
    duration: "22:15",
    status: "draft"
  },
];

const RecentVideosSection = () => {
  const [activeTab, setActiveTab] = useState("published");
  
  const publishedVideos = recentVideosData.filter(video => video.status === "published");
  const draftVideos = recentVideosData.filter(video => video.status === "draft");
  
  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Video className="h-5 w-5 text-brand-purple" />
          <CardTitle className="text-lg font-semibold">Recent Videos</CardTitle>
        </div>
        <Button variant="outline" size="sm">
          View All Videos
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="published" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="published">Published ({publishedVideos.length})</TabsTrigger>
            <TabsTrigger value="draft">Drafts ({draftVideos.length})</TabsTrigger>
          </TabsList>
          
          <TabsContent value="published" className="space-y-4">
            {publishedVideos.map(video => (
              <VideoCard key={video.id} video={video} />
            ))}
          </TabsContent>
          
          <TabsContent value="draft" className="space-y-4">
            {draftVideos.map(video => (
              <VideoCard key={video.id} video={video} />
            ))}
            {draftVideos.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <p>No draft videos found.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

interface VideoCardProps {
  video: {
    id: string;
    title: string;
    thumbnail: string;
    publishDate: string;
    views: number;
    likes: number;
    comments: number;
    duration: string;
    status: string;
  };
}

const VideoCard = ({ video }: VideoCardProps) => {
  const isPublished = video.status === "published";
  const publishDate = new Date(video.publishDate);
  const formattedDate = publishDate.toLocaleDateString('en-US', {
    year: 'numeric', 
    month: 'short', 
    day: 'numeric'
  });
  
  return (
    <div className="flex gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors">
      <div className="relative flex-shrink-0">
        <img
          src={video.thumbnail}
          alt={video.title}
          className="w-32 h-20 object-cover rounded-md"
        />
        <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
          {video.duration}
        </div>
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-start">
          <h3 className="font-medium text-sm mb-1 truncate pr-4">{video.title}</h3>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>View Details</DropdownMenuItem>
              <DropdownMenuItem>Edit Video</DropdownMenuItem>
              <DropdownMenuItem>Share</DropdownMenuItem>
              {!isPublished && <DropdownMenuItem>Publish</DropdownMenuItem>}
              <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        <p className="text-xs text-muted-foreground mb-2">{formattedDate}</p>
        
        {isPublished ? (
          <div className="flex gap-3 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Eye className="h-3 w-3" />
              <span>{video.views.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <ThumbsUp className="h-3 w-3" />
              <span>{video.likes.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="h-3 w-3" />
              <span>{video.comments.toLocaleString()}</span>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>Draft - Continue editing</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentVideosSection;
