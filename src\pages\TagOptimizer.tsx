import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Header from "@/components/dashboard/Header";
import Sidebar from "@/components/dashboard/Sidebar";
import { useVideo, VideoProvider } from "@/contexts/VideoContext";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import TagOptimizerContainer from "../components/tag-optimizer/TagOptimizerContainer";
import { generateTags, TagGenerationRequest } from "@/lib/geminiApi";
import { TagFormData } from "@/components/tag-optimizer/TagOptimizerInputs";

// Inner component to use the useVideo hook
const TagOptimizerContent = () => {
  const { videoData } = useVideo();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [generatedTags, setGeneratedTags] = useState<string[]>([]);
  const [videoDetails, setVideoDetails] = useState({
    title: "",
    description: ""
  });

  // Check if we have video data from context or location state
  useEffect(() => {
    const videoDataSource = videoData || location.state?.videoData;
    
    if (videoDataSource) {
      // Set video details from the data
      setVideoDetails({
        title: videoDataSource.potentialTitle || "",
        description: videoDataSource.description || ""
      });
    } else {
      toast({
        variant: "destructive",
        title: "No video selected",
        description: "Please select a video first.",
      });
      navigate("/ai-features");
    }
  }, [videoData, navigate, location.state]);

  const handleBack = () => {
    // Pass video data from either context or location state when navigating back
    navigate('/ai-features', { state: { videoData: videoData || location.state?.videoData } });
  };  const handleGenerateTags = async (tagData: TagFormData) => {
    setIsLoading(true);
    
    try {
      // Get video niche from context or location state
      const videoDataSource = videoData || location.state?.videoData;
      const videoNiche = videoDataSource?.niche || "";
      
      // Prepare request for the Gemini API
      const request: TagGenerationRequest = {
        title: tagData.title,
        description: tagData.description,
        competitorUrl: tagData.competitorUrl,
        specificKeywords: tagData.specificKeywords,
        tagCount: tagData.tagCount,
        includeNicheTags: tagData.includeNicheTags,
        includeBrandTags: tagData.includeBrandTags,
        localLanguage: tagData.localLanguage,
        niche: videoNiche
      };
      
      // Call the Gemini API
      const response = await generateTags(request);
      
      if (response && response.tags.length > 0) {
        setGeneratedTags(response.tags);
        
        toast({
          title: "Tags Generated",
          description: `Successfully generated ${response.tags.length} tags for your video.`,
        });
      } else {
        throw new Error("Failed to generate tags. Please try again.");
      }    } catch (error: any) {
      // Extract more informative error message if available
      let errorMessage = "Failed to generate tags. Please try again.";
      
      if (error.message) {
        if (error.message.includes("404") || error.message.includes("model")) {
          errorMessage = "API model not available. Our team has been notified.";
        } else if (error.message.includes("429")) {
          errorMessage = "Too many requests. Please try again in a minute.";
        } else if (error.message.includes("403") || error.message.includes("401")) {
          errorMessage = "API authentication error. Please check your API key.";
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        variant: "destructive",
        title: "Error Generating Tags",
        description: errorMessage,
      });
      console.error("Error generating tags:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetTags = () => {
    setGeneratedTags([]);
  };
  
  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <div className="w-64 hidden lg:block">
        <Sidebar />
      </div>
      <div className="lg:hidden">
        <Sidebar />
      </div>

      <div className="flex-1 flex flex-col lg:pt-0 pt-16">
        <Header
          title="Tag Optimizer"
          subtitle="Generate high-performing tags to boost your video's discoverability"
        />          <main className="flex-1 p-4 md:p-8">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Back button */}
            <Button 
              variant="outline"
              size="sm"
              onClick={handleBack}
              className="mb-2 flex items-center gap-1"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>

            <TagOptimizerContainer
              videoDetails={videoDetails}
              isLoading={isLoading}
              generatedTags={generatedTags}
              onGenerateTags={handleGenerateTags}
              onResetTags={resetTags}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

// Wrap with VideoProvider at the top level
const TagOptimizer = () => {
  return (
    <VideoProvider>
      <TagOptimizerContent />
    </VideoProvider>
  );
};

export default TagOptimizer;
