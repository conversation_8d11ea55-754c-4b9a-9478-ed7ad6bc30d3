import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";

interface CreateTitlesButtonProps {
  onClick: () => void;
  isLoading: boolean;
  disabled?: boolean;
}

const CreateTitlesButton: React.FC<CreateTitlesButtonProps> = ({
  onClick,
  isLoading,
  disabled = false
}) => {
  return (
    <Button 
      onClick={onClick}
      className="bg-brand-purple hover:bg-brand-purple/90 w-full"
      disabled={isLoading || disabled}
    >
      {isLoading ? (
        <span className="flex items-center gap-2">
          <span className="h-4 w-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></span>
          Generating AI Titles...
        </span>
      ) : (
        <span className="flex items-center gap-2">
          <Sparkles className="h-4 w-4" />
          Generate AI Titles
        </span>
      )}
    </Button>
  );
};

export default CreateTitlesButton;
