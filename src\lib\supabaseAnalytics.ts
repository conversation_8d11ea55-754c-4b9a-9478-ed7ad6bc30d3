/**
 * Supabase service for managing YouTube analytics data
 */

import { supabase } from "@/integrations/supabase/client";
import { ChannelAnalytics, VideoAnalytics } from "@/lib/youtubeAnalytics";

export interface YouTubeChannel {
  id: string;
  user_id: string;
  channel_id: string;
  channel_title: string;
  channel_description?: string;
  subscriber_count: number;
  video_count: number;
  view_count: number;
  connected_at: string;
  updated_at: string;
}

export interface ChannelAnalyticsData {
  id: string;
  channel_id: string;
  date: string;
  views: number;
  watch_time_minutes: number;
  subscribers_gained: number;
  subscribers_lost: number;
  likes: number;
  comments: number;
  shares: number;
  estimated_revenue?: number;
}

/**
 * Connect a YouTube channel to the user's account
 */
export async function connectYouTubeChannel(
  channelId: string,
  channelTitle: string,
  channelDescription: string,
  subscriberCount: number,
  videoCount: number,
  viewCount: number,
  accessToken: string,
  refreshToken: string,
  expiresAt: Date
): Promise<YouTubeChannel | null> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error("User not authenticated");

    const { data, error } = await supabase
      .from('youtube_channels')
      .upsert({
        user_id: user.user.id,
        channel_id: channelId,
        channel_title: channelTitle,
        channel_description: channelDescription,
        subscriber_count: subscriberCount,
        video_count: videoCount,
        view_count: viewCount,
        access_token: accessToken, // In production, encrypt this
        refresh_token: refreshToken, // In production, encrypt this
        token_expires_at: expiresAt.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;

  } catch (error: any) {
    console.error("Error connecting YouTube channel:", error);
    return null;
  }
}

/**
 * Get user's connected YouTube channels
 */
export async function getUserYouTubeChannels(): Promise<YouTubeChannel[]> {
  try {
    const { data, error } = await supabase
      .from('youtube_channels')
      .select('*')
      .order('connected_at', { ascending: false });

    if (error) throw error;
    return data || [];

  } catch (error: any) {
    console.error("Error fetching YouTube channels:", error);
    return [];
  }
}

/**
 * Store daily analytics data
 */
export async function storeChannelAnalytics(
  channelDbId: string,
  date: string,
  analytics: ChannelAnalytics
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('channel_analytics')
      .upsert({
        channel_id: channelDbId,
        date,
        views: analytics.views,
        watch_time_minutes: analytics.watchTimeMinutes,
        subscribers_gained: analytics.subscribers,
        likes: analytics.likes,
        comments: analytics.comments,
        shares: analytics.shares,
        estimated_revenue: analytics.estimatedRevenue || 0,
      });

    if (error) throw error;
    return true;

  } catch (error: any) {
    console.error("Error storing channel analytics:", error);
    return false;
  }
}

/**
 * Get channel analytics for a date range
 */
export async function getChannelAnalyticsFromDB(
  channelDbId: string,
  startDate: string,
  endDate: string
): Promise<ChannelAnalyticsData[]> {
  try {
    const { data, error } = await supabase
      .from('channel_analytics')
      .select('*')
      .eq('channel_id', channelDbId)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: false });

    if (error) throw error;
    return data || [];

  } catch (error: any) {
    console.error("Error fetching channel analytics:", error);
    return [];
  }
}

/**
 * Store video analytics data
 */
export async function storeVideoAnalytics(
  channelDbId: string,
  videos: VideoAnalytics[]
): Promise<boolean> {
  try {
    const videoData = videos.map(video => ({
      channel_id: channelDbId,
      video_id: video.videoId,
      title: video.title,
      published_at: video.publishedAt,
      thumbnail_url: video.thumbnailUrl,
      views: video.views,
      watch_time_minutes: video.watchTimeMinutes,
      likes: video.likes,
      comments: video.comments,
      shares: video.shares,
      last_updated: new Date().toISOString(),
    }));

    const { error } = await supabase
      .from('video_analytics')
      .upsert(videoData);

    if (error) throw error;
    return true;

  } catch (error: any) {
    console.error("Error storing video analytics:", error);
    return false;
  }
}

/**
 * Get top performing videos from database
 */
export async function getTopVideosFromDB(
  channelDbId: string,
  limit: number = 10
): Promise<VideoAnalytics[]> {
  try {
    const { data, error } = await supabase
      .from('video_analytics')
      .select('*')
      .eq('channel_id', channelDbId)
      .order('views', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return (data || []).map(video => ({
      videoId: video.video_id,
      title: video.title,
      views: video.views,
      watchTimeMinutes: video.watch_time_minutes,
      likes: video.likes,
      comments: video.comments,
      shares: video.shares,
      publishedAt: video.published_at,
      thumbnailUrl: video.thumbnail_url,
    }));

  } catch (error: any) {
    console.error("Error fetching top videos:", error);
    return [];
  }
}

/**
 * Get aggregated analytics for the user's primary channel
 */
export async function getAggregatedAnalytics(
  days: number = 30
): Promise<ChannelAnalytics | null> {
  try {
    const channels = await getUserYouTubeChannels();
    if (channels.length === 0) return null;

    const primaryChannel = channels[0]; // Use the first connected channel
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const analytics = await getChannelAnalyticsFromDB(
      primaryChannel.id,
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    );

    if (analytics.length === 0) return null;

    // Aggregate the data
    const totalViews = analytics.reduce((sum, day) => sum + day.views, 0);
    const totalWatchTime = analytics.reduce((sum, day) => sum + day.watch_time_minutes, 0);
    const totalLikes = analytics.reduce((sum, day) => sum + day.likes, 0);
    const totalComments = analytics.reduce((sum, day) => sum + day.comments, 0);
    const totalShares = analytics.reduce((sum, day) => sum + day.shares, 0);
    const totalSubscribersGained = analytics.reduce((sum, day) => sum + day.subscribers_gained, 0);

    // Calculate changes (compare with previous period)
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);
    
    const previousAnalytics = await getChannelAnalyticsFromDB(
      primaryChannel.id,
      previousStartDate.toISOString().split('T')[0],
      startDate.toISOString().split('T')[0]
    );

    const previousViews = previousAnalytics.reduce((sum, day) => sum + day.views, 0);
    const previousSubscribers = previousAnalytics.reduce((sum, day) => sum + day.subscribers_gained, 0);

    const viewsChange = previousViews > 0 ? ((totalViews - previousViews) / previousViews) * 100 : 0;
    const subscribersChange = previousSubscribers > 0 ? ((totalSubscribersGained - previousSubscribers) / previousSubscribers) * 100 : 0;

    return {
      views: totalViews,
      watchTimeMinutes: totalWatchTime,
      subscribers: totalSubscribersGained,
      likes: totalLikes,
      comments: totalComments,
      shares: totalShares,
      viewsChange: Math.round(viewsChange * 100) / 100,
      subscribersChange: Math.round(subscribersChange * 100) / 100,
    };

  } catch (error: any) {
    console.error("Error getting aggregated analytics:", error);
    return null;
  }
}
