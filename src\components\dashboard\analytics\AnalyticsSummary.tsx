import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { 
  TrendingUp, 
  Users, 
  Clock, 
  ThumbsUp,
  Eye,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react";
import { Progress } from "@/components/ui/progress";

// Mock analytics data
const analyticsMockData = {
  views: {
    value: 24580,
    change: 12.5,
    positive: true
  },
  watchTime: {
    value: 1250, // hours
    change: 8.3,
    positive: true
  },
  subscribers: {
    value: 1842,
    change: 3.7,
    positive: true
  },
  likes: {
    value: 3250,
    change: -2.1,
    positive: false
  }
};

const AnalyticsSummary = () => {
  return (
    <div className="space-y-6">
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <AnalyticsCard
          title="Total Views"
          value={analyticsMockData.views.value.toLocaleString()}
          change={analyticsMockData.views.change}
          isPositive={analyticsMockData.views.positive}
          icon={<Eye className="h-5 w-5" />}
          color="blue"
        />
        <AnalyticsCard
          title="Watch Time"
          value={`${analyticsMockData.watchTime.value.toLocaleString()} hrs`}
          change={analyticsMockData.watchTime.change}
          isPositive={analyticsMockData.watchTime.positive}
          icon={<Clock className="h-5 w-5" />}
          color="purple"
        />
        <AnalyticsCard
          title="Subscribers"
          value={analyticsMockData.subscribers.value.toLocaleString()}
          change={analyticsMockData.subscribers.change}
          isPositive={analyticsMockData.subscribers.positive}
          icon={<Users className="h-5 w-5" />}
          color="green"
        />
        <AnalyticsCard
          title="Total Likes"
          value={analyticsMockData.likes.value.toLocaleString()}
          change={analyticsMockData.likes.change}
          isPositive={analyticsMockData.likes.positive}
          icon={<ThumbsUp className="h-5 w-5" />}
          color="amber"
        />
      </div>
      
      {/* Growth Chart */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="font-medium mb-1">Monthly Growth</h3>
              <p className="text-sm text-muted-foreground">Channel performance over time</p>
            </div>
            <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 px-2 py-1 rounded-md">
              <TrendingUp className="h-3 w-3" />
              <span>+8.2% growth</span>
            </div>
          </div>
          
          {/* Mock bar chart - In a real app, use a proper chart library */}
          <div className="space-y-3">
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Views</span>
                <span className="text-muted-foreground">24.5K</span>
              </div>
              <Progress value={85} className="h-2 bg-blue-100" />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Watch Time</span>
                <span className="text-muted-foreground">1.25K hrs</span>
              </div>
              <Progress value={65} className="h-2 bg-purple-100" />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Subscribers</span>
                <span className="text-muted-foreground">1.84K</span>
              </div>
              <Progress value={42} className="h-2 bg-green-100" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface AnalyticsCardProps {
  title: string;
  value: string;
  change: number;
  isPositive: boolean;
  icon: React.ReactNode;
  color: "blue" | "purple" | "green" | "amber";
}

const AnalyticsCard = ({ title, value, change, isPositive, icon, color }: AnalyticsCardProps) => {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-600",
    purple: "bg-purple-50 text-purple-600",
    green: "bg-green-50 text-green-600",
    amber: "bg-amber-50 text-amber-600"
  };
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-2">
          <div className={`${colorClasses[color]} p-2 rounded-md`}>
            {icon}
          </div>
          <div className={`flex items-center gap-1 text-xs ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {isPositive ? <ArrowUpRight className="h-3 w-3" /> : <ArrowDownRight className="h-3 w-3" />}
            <span>{Math.abs(change)}%</span>
          </div>
        </div>
        <h3 className="text-2xl font-bold mb-1">{value}</h3>
        <p className="text-sm text-muted-foreground">{title}</p>
      </CardContent>
    </Card>
  );
};

export default AnalyticsSummary;