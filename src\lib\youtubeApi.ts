/**
 * YouTube Data API client for fetching trending videos and metadata
 * 
 * This file provides functions for interacting with the YouTube Data API,
 * primarily focused on fetching trending videos and video metadata.
 */

import { API_CONFIG, API_DEFAULTS } from "@/lib/config";
import { toast } from "@/components/ui/use-toast";

interface YouTubeVideo {
  id?: string;
  title: string;
  description?: string;
  channelTitle?: string;
  publishedAt?: string;
  thumbnails?: {
    default?: { url: string, width: number, height: number };
    medium?: { url: string, width: number, height: number };
    high?: { url: string, width: number, height: number };
  };
  viewCount?: number;
  likeCount?: number;
}

interface YouTubeTopVideosResponse {
  trending: YouTubeVideo[];
  mostViewed: YouTubeVideo[];
}

/**
 * Fetches top trending and most viewed videos for a specific category
 * 
 * @param categoryId - The YouTube category ID to fetch videos for
 * @param maxResults - Maximum number of videos to return (default from config)
 * @returns Object with arrays of trending and most viewed videos
 */
export async function getTopVideosByCategory(
  categoryId: string,
  maxResults: number = API_DEFAULTS.youtube.maxResults
): Promise<YouTubeTopVideosResponse | null> {
  const apiKey = API_CONFIG.youtube.apiKey;
  
  if (!apiKey || apiKey === "YOUR_YOUTUBE_API_KEY") {
    toast({
      variant: "destructive",
      title: "YouTube API Key Not Found",
      description: "Please set your YouTube API key in the configuration.",
    });
    return null;
  }

  try {
    // Get trending videos
    console.log(`Fetching top ${maxResults} trending videos in category ID: ${categoryId}`);
    const trendingUrl = new URL("https://www.googleapis.com/youtube/v3/videos");
    trendingUrl.searchParams.append("part", "snippet,statistics");
    trendingUrl.searchParams.append("chart", "mostPopular");
    trendingUrl.searchParams.append("videoCategoryId", categoryId);
    trendingUrl.searchParams.append("maxResults", maxResults.toString());
    trendingUrl.searchParams.append("key", apiKey);

    const trendingResponse = await fetch(trendingUrl.toString());
    if (!trendingResponse.ok) {
      throw new Error(`YouTube API error: ${trendingResponse.statusText}`);
    }
    
    const trendingData = await trendingResponse.json();
    const trendingVideos: YouTubeVideo[] = trendingData.items.map((item: any) => ({
      id: item.id,
      title: item.snippet.title,
      description: item.snippet.description,
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt,
      thumbnails: item.snippet.thumbnails,
      viewCount: parseInt(item.statistics?.viewCount || "0"),
      likeCount: parseInt(item.statistics?.likeCount || "0")
    }));
    
    console.log(`Found ${trendingVideos.length} trending videos.`);
    
    // Get most viewed videos
    console.log(`Fetching top ${maxResults} most viewed videos in category ID: ${categoryId}`);
    const mostViewedUrl = new URL("https://www.googleapis.com/youtube/v3/search");
    mostViewedUrl.searchParams.append("part", "snippet");
    mostViewedUrl.searchParams.append("type", "video");
    mostViewedUrl.searchParams.append("videoCategoryId", categoryId);
    mostViewedUrl.searchParams.append("order", "viewCount");
    mostViewedUrl.searchParams.append("maxResults", maxResults.toString());
    mostViewedUrl.searchParams.append("key", apiKey);

    const mostViewedResponse = await fetch(mostViewedUrl.toString());
    if (!mostViewedResponse.ok) {
      throw new Error(`YouTube API error: ${mostViewedResponse.statusText}`);
    }
    
    const mostViewedData = await mostViewedResponse.json();
    const videoIds = mostViewedData.items.map((item: any) => item.id.videoId).join(',');

    // Get video statistics for the most viewed videos
    if (videoIds) {
      const statsUrl = new URL("https://www.googleapis.com/youtube/v3/videos");
      statsUrl.searchParams.append("part", "statistics");
      statsUrl.searchParams.append("id", videoIds);
      statsUrl.searchParams.append("key", apiKey);
      
      const statsResponse = await fetch(statsUrl.toString());
      const statsData = await statsResponse.json();
      
      // Create a map of video ID to statistics
      const statsMap = new Map();
      statsData.items.forEach((item: any) => {
        statsMap.set(item.id, {
          viewCount: parseInt(item.statistics?.viewCount || "0"),
          likeCount: parseInt(item.statistics?.likeCount || "0")
        });
      });
      
      // Add statistics to most viewed videos
      const mostViewedVideos: YouTubeVideo[] = mostViewedData.items.map((item: any) => {
        const stats = statsMap.get(item.id.videoId) || {};
        return {
          id: item.id.videoId,
          title: item.snippet.title,
          description: item.snippet.description,
          channelTitle: item.snippet.channelTitle,
          publishedAt: item.snippet.publishedAt,
          thumbnails: item.snippet.thumbnails,
          viewCount: stats.viewCount || 0,
          likeCount: stats.likeCount || 0
        };
      });
      
      console.log(`Found ${mostViewedVideos.length} most viewed videos.`);
      
      return {
        trending: trendingVideos,
        mostViewed: mostViewedVideos
      };
    } else {
      return {
        trending: trendingVideos,
        mostViewed: []
      };
    }
  } catch (error: any) {
    console.error(`YouTube API error: ${error.message}`);
    toast({
      variant: "destructive",
      title: "YouTube API Error",
      description: "Failed to fetch videos from YouTube. Please check your API key and try again.",
    });
    return null;
  }
}

/**
 * Extracts useful video titles from the response for title generation
 * 
 * @param response - The response from getTopVideosByCategory
 * @returns Array of video titles
 */
export function extractVideoTitles(response: YouTubeTopVideosResponse): string[] {
  if (!response) return [];
  
  // Combine both trending and most viewed titles
  const allTitles: string[] = [
    ...response.trending.map(video => video.title),
    ...response.mostViewed.map(video => video.title)
  ];
  
  // Remove duplicates
  const uniqueTitles = [...new Set(allTitles)];
  
  return uniqueTitles;
}

/**
 * Gets video metadata from a YouTube video URL
 * 
 * @param youtubeUrl - URL of the YouTube video
 * @returns Video metadata or null if error
 */
export async function getVideoMetadata(youtubeUrl: string): Promise<YouTubeVideo | null> {
  const apiKey = API_CONFIG.youtube.apiKey;
  
  if (!apiKey || apiKey === "YOUR_YOUTUBE_API_KEY") {
    toast({
      variant: "destructive",
      title: "YouTube API Key Not Found",
      description: "Please set your YouTube API key in the configuration.",
    });
    return null;
  }

  try {
    // Extract video ID from URL
    const videoId = extractVideoId(youtubeUrl);
    if (!videoId) {
      throw new Error("Invalid YouTube URL");
    }
    
    // Get video data
    const url = new URL("https://www.googleapis.com/youtube/v3/videos");
    url.searchParams.append("part", "snippet,statistics");
    url.searchParams.append("id", videoId);
    url.searchParams.append("key", apiKey);
    
    const response = await fetch(url.toString());
    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.items || data.items.length === 0) {
      throw new Error("Video not found");
    }
    
    const item = data.items[0];
    return {
      id: item.id,
      title: item.snippet.title,
      description: item.snippet.description,
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt,
      thumbnails: item.snippet.thumbnails,
      viewCount: parseInt(item.statistics.viewCount),
      likeCount: parseInt(item.statistics.likeCount)
    };
  } catch (error: any) {
    console.error(`Error fetching video metadata: ${error.message}`);
    return null;
  }
}

/**
 * Extract video ID from a YouTube URL
 * 
 * @param url - YouTube URL
 * @returns Video ID or null if not found
 */
function extractVideoId(url: string): string | null {
  try {
    // Handle different YouTube URL formats
    // Full URL: https://www.youtube.com/watch?v=VIDEO_ID
    // Short URL: https://youtu.be/VIDEO_ID
    // Embedded URL: https://www.youtube.com/embed/VIDEO_ID
    
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    if (hostname === 'youtu.be') {
      return urlObj.pathname.substring(1);
    }
    
    if (hostname.includes('youtube.com')) {
      if (urlObj.pathname === '/watch') {
        return urlObj.searchParams.get('v');
      }
      
      if (urlObj.pathname.startsWith('/embed/')) {
        return urlObj.pathname.split('/')[2];
      }
    }
    
    return null;
  } catch (error) {
    return null;
  }
}