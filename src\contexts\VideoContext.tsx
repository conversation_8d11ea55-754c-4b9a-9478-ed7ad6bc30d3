import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the shape of our context
type VideoData = {
  type: 'file' | 'url';
  data: File | string;
  potentialTitle?: string;
  description?: string;
  niche?: string;
} | null;

type VideoContextType = {
  videoData: VideoData;
  setVideoData: (data: VideoData) => void;
  isProcessing: boolean;
  setIsProcessing: (isProcessing: boolean) => void;
  clearVideo: () => void;
};

// Create the context with a default value
const VideoContext = createContext<VideoContextType | undefined>(undefined);

// Create a provider component
export const VideoProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [videoData, setVideoData] = useState<VideoData>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const clearVideo = () => {
    setVideoData(null);
  };

  return (
    <VideoContext.Provider value={{ videoData, setVideoData, isProcessing, setIsProcessing, clearVideo }}>
      {children}
    </VideoContext.Provider>
  );
};

// Create a hook to use the context
export const useVideo = (): VideoContextType => {
  const context = useContext(VideoContext);
  if (context === undefined) {
    throw new Error('useVideo must be used within a VideoProvider');
  }
  return context;
};
