
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowRight, Video, FileText, Image, Tag, BarChart, Search } from "lucide-react";

type FeatureCardProps = {
  title: string;
  description: string;
  icon: string;
  linkText: string;
  linkUrl: string;
  disabled?: boolean;
};

const FeatureCard = ({ title, description, icon, linkText, linkUrl, disabled = false }: FeatureCardProps) => {
  const renderIcon = () => {
    switch (icon) {
      case "video":
        return <Video className="h-6 w-6 text-primary" />;
      case "file-text":
        return <FileText className="h-6 w-6 text-primary" />;
      case "image":
        return <Image className="h-6 w-6 text-primary" />;
      case "tag":
        return <Tag className="h-6 w-6 text-primary" />;
      case "bar-chart":
        return <BarChart className="h-6 w-6 text-primary" />;
      case "search":
        return <Search className="h-6 w-6 text-primary" />;
      default:
        return <Video className="h-6 w-6 text-primary" />;
    }
  };

  return (
    <div className="bg-white rounded-lg border shadow-sm p-6 transition-all hover:shadow">
      <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
        {renderIcon()}
      </div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6">{description}</p>
      
      {disabled ? (
        <Button variant="outline" disabled className="w-full justify-between">
          {linkText} <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      ) : (
        <Link to={linkUrl}>
          <Button variant="outline" className="w-full justify-between">
            {linkText} <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      )}
    </div>
  );
};

export default FeatureCard;
