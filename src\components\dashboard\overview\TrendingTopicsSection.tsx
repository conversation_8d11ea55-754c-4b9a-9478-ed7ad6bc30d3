import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { TrendingUp, ArrowUp } from "lucide-react";
import { Button } from "@/components/ui/button";

// Mock data for trending topics
const trendingTopicsData = [
  {
    id: 1,
    topic: "AI Video Editing Tools",
    growth: 128,
    category: "Technology"
  },
  {
    id: 2,
    topic: "YouTube Shorts Strategy",
    growth: 87,
    category: "Content Strategy"
  },
  {
    id: 3,
    topic: "Behind-the-Scenes Content",
    growth: 64,
    category: "Content Format"
  },
  {
    id: 4,
    topic: "Reaction Videos",
    growth: 42,
    category: "Entertainment"
  }
];

const TrendingTopicsSection = () => {
  return (
    <Card className="h-full">
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-red-500" />
          <CardTitle className="text-lg font-semibold">Trending Topics</CardTitle>
        </div>
        <Button variant="ghost" size="sm" className="text-xs text-muted-foreground">
          Explore more
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {trendingTopicsData.map((topic) => (
            <div 
              key={topic.id} 
              className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-900 rounded-lg transition-colors cursor-pointer"
            >
              <div className="flex flex-col">
                <span className="font-medium text-sm">{topic.topic}</span>
                <span className="text-xs text-muted-foreground">{topic.category}</span>
              </div>
              <div className="flex items-center gap-1 text-green-600 bg-green-50 px-2 py-0.5 rounded text-xs">
                <ArrowUp className="h-3 w-3" />
                <span>{topic.growth}%</span>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-3 border-t">
          <h4 className="text-sm font-medium mb-2">Top Categories Rising</h4>
          <div className="flex gap-2 flex-wrap">
            <Badge color="blue">Technology</Badge>
            <Badge color="green">Education</Badge>
            <Badge color="purple">Gaming</Badge>
            <Badge color="amber">Lifestyle</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface BadgeProps {
  children: React.ReactNode;
  color: "blue" | "green" | "purple" | "amber" | "red";
}

const Badge = ({ children, color }: BadgeProps) => {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-600",
    green: "bg-green-50 text-green-600",
    purple: "bg-purple-50 text-purple-600",
    amber: "bg-amber-50 text-amber-600",
    red: "bg-red-50 text-red-600"
  };
  
  return (
    <span className={`${colorClasses[color]} text-xs px-2 py-1 rounded-full`}>
      {children}
    </span>
  );
};

export default TrendingTopicsSection;
