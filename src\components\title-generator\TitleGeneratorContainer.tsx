import React from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import TitleGeneratorInputs from "./TitleGeneratorInputs";
import ErrorDisplay from "./ErrorDisplay";
import TitleGeneratorActions from "./TitleGeneratorActions";
import TrendingTitlesList from "./TrendingTitlesList";
import TitleSuggestions from "./TitleSuggestions";
import GeneratedTitlePlaceholder from "./GeneratedTitlePlaceholder";

interface VideoDetails {
  title: string;
  description: string;
}

interface TitleGeneratorContainerProps {
  videoNiche: string;
  videoDetails: VideoDetails;
  error: string | null;
  isLoading: boolean;
  isGeneratingAI: boolean;
  trendingVideos: any[];
  generatedTitles: string[];
  onBack: () => void;
  onNicheChange: (niche: string) => void;
  onDetailsChange: (details: VideoDetails) => void;
  onRefresh: () => void;
  onGenerate: () => void;
  onResetTitles: () => void;
}

const TitleGeneratorContainer: React.FC<TitleGeneratorContainerProps> = ({
  videoNiche,
  videoDetails,
  error,
  isLoading,
  isGeneratingAI,
  trendingVideos,
  generatedTitles,
  onBack,
  onNicheChange,
  onDetailsChange,
  onRefresh,
  onGenerate,
  onResetTitles,
}) => {
  return (
    <div className="max-w-6xl mx-auto">
      {/* Back button */}
      <Button
        variant="outline"
        size="sm"
        onClick={onBack}
        className="mb-4 flex items-center gap-1"
      >
        <ArrowLeft className="h-4 w-4" />
        Back
      </Button>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Generate Optimized Titles</h2>

        <TitleGeneratorInputs
          videoNiche={videoNiche}
          videoDetails={videoDetails}
          onNicheChange={onNicheChange}
          onDetailsChange={onDetailsChange}
        />

        <ErrorDisplay error={error} />

        <TitleGeneratorActions
          onRefresh={onRefresh}
          onGenerate={onGenerate}
          isLoading={isLoading}
          isGeneratingAI={isGeneratingAI}
          hasTrendingVideos={trendingVideos.length > 0}
        />

        <div className="grid md:grid-cols-2 gap-6">
          <TrendingTitlesList titles={trendingVideos} isLoading={isLoading} />

          {generatedTitles.length > 0 ? (
            <TitleSuggestions titles={generatedTitles} onReset={onResetTitles} />
          ) : (
            <GeneratedTitlePlaceholder isLoading={isGeneratingAI} />
          )}
        </div>
      </div>
    </div>
  );
};

export default TitleGeneratorContainer;
