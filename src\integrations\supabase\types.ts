export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          id: string
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          id: string
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          id?: string
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      youtube_channels: {
        Row: {
          id: string
          user_id: string
          channel_id: string
          channel_title: string | null
          channel_description: string | null
          subscriber_count: number
          video_count: number
          view_count: number
          access_token: string | null
          refresh_token: string | null
          token_expires_at: string | null
          connected_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          channel_id: string
          channel_title?: string | null
          channel_description?: string | null
          subscriber_count?: number
          video_count?: number
          view_count?: number
          access_token?: string | null
          refresh_token?: string | null
          token_expires_at?: string | null
          connected_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          channel_id?: string
          channel_title?: string | null
          channel_description?: string | null
          subscriber_count?: number
          video_count?: number
          view_count?: number
          access_token?: string | null
          refresh_token?: string | null
          token_expires_at?: string | null
          connected_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      channel_analytics: {
        Row: {
          id: string
          channel_id: string
          date: string
          views: number
          watch_time_minutes: number
          subscribers_gained: number
          subscribers_lost: number
          likes: number
          comments: number
          shares: number
          estimated_revenue: number
          created_at: string
        }
        Insert: {
          id?: string
          channel_id: string
          date: string
          views?: number
          watch_time_minutes?: number
          subscribers_gained?: number
          subscribers_lost?: number
          likes?: number
          comments?: number
          shares?: number
          estimated_revenue?: number
          created_at?: string
        }
        Update: {
          id?: string
          channel_id?: string
          date?: string
          views?: number
          watch_time_minutes?: number
          subscribers_gained?: number
          subscribers_lost?: number
          likes?: number
          comments?: number
          shares?: number
          estimated_revenue?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "channel_analytics_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "youtube_channels"
            referencedColumns: ["id"]
          }
        ]
      }
      video_analytics: {
        Row: {
          id: string
          channel_id: string
          video_id: string
          title: string
          description: string | null
          published_at: string | null
          duration_seconds: number | null
          thumbnail_url: string | null
          views: number
          watch_time_minutes: number
          likes: number
          dislikes: number
          comments: number
          shares: number
          click_through_rate: number
          average_view_duration_seconds: number
          last_updated: string
          created_at: string
        }
        Insert: {
          id?: string
          channel_id: string
          video_id: string
          title: string
          description?: string | null
          published_at?: string | null
          duration_seconds?: number | null
          thumbnail_url?: string | null
          views?: number
          watch_time_minutes?: number
          likes?: number
          dislikes?: number
          comments?: number
          shares?: number
          click_through_rate?: number
          average_view_duration_seconds?: number
          last_updated?: string
          created_at?: string
        }
        Update: {
          id?: string
          channel_id?: string
          video_id?: string
          title?: string
          description?: string | null
          published_at?: string | null
          duration_seconds?: number | null
          thumbnail_url?: string | null
          views?: number
          watch_time_minutes?: number
          likes?: number
          dislikes?: number
          comments?: number
          shares?: number
          click_through_rate?: number
          average_view_duration_seconds?: number
          last_updated?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "video_analytics_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "youtube_channels"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
