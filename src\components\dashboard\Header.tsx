// filepath: c:\Users\<USER>\Desktop\video-growth-ai-lab\src\components\dashboard\Header.tsx
import React from "react";
import { Button } from "@/components/ui/button";
import { Bell, Search } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type HeaderProps = {
  title: string;
  subtitle?: string;
};

const Header = ({ title, subtitle }: HeaderProps) => {
  const { user, signOut } = useAuth();
  
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center p-4 md:p-6 border-b border-gray-100 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm">
      <div>
        <h1 className="text-2xl font-bold bg-gradient-to-r from-brand-purple to-brand-blue text-transparent bg-clip-text">
          {title}
        </h1>
        {subtitle && (
          <p className="text-muted-foreground text-sm">{subtitle}</p>
        )}
      </div>
      
      <div className="flex items-center gap-3 mt-4 md:mt-0">
        <div className="relative hidden md:block">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search..."
            className="bg-gray-100 dark:bg-gray-800 border-0 rounded-full h-9 pl-9 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-brand-purple/30"
          />
        </div>

        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="flex gap-2 items-center">
              <div className="h-6 w-6 rounded-full bg-brand-purple/10 flex items-center justify-center">
                <span>{user?.email?.charAt(0) || 'U'}</span>
              </div>
              <span className="hidden md:inline">{user?.email?.split('@')[0] || 'My Account'}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Profile</DropdownMenuItem>
            <DropdownMenuItem>Settings</DropdownMenuItem>
            <DropdownMenuItem>Billing</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => signOut()}>Log out</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default Header;
