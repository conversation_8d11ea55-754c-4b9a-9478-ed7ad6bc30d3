import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import CreateTitlesButton from "@/components/title-generator/CreateTitlesButton";

interface TitleGeneratorActionsProps {
  onRefresh: () => void;
  onGenerate: () => void;
  isLoading: boolean;
  isGeneratingAI: boolean;
  hasTrendingVideos: boolean;
}

const TitleGeneratorActions: React.FC<TitleGeneratorActionsProps> = ({
  onRefresh,
  onGenerate,
  isLoading,
  isGeneratingAI,
  hasTrendingVideos,
}) => {
  return (
    <div className="flex flex-wrap gap-2 mb-6">
      <Button
        onClick={onRefresh}
        variant="outline"
        size="sm"
        disabled={isLoading}
        className="flex items-center gap-1"
      >
        {isLoading ? (
          <span className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-1"></span>
        ) : (
          <RefreshCw className="h-3 w-3 mr-1" />
        )}
        {isLoading ? "Loading..." : "Refresh Trending"}
      </Button>

      <CreateTitlesButton
        onClick={onGenerate}
        isLoading={isGeneratingAI}
        disabled={!hasTrendingVideos || isLoading}
      />
    </div>
  );
};

export default TitleGeneratorActions;
