/**
 * Master API file for VideoGrowth AI
 *
 * This file contains all the API functions for different features:
 * - Title generation
 * - Thumbnail generation
 * - Description writing
 * - Tag optimization
 */

import { toast } from "@/components/ui/use-toast";

// API Configuration
const API_BASE_URL = "https://api.videogrowth.ai"; // Replace with your actual API base URL
const API_TIMEOUT = 30000; // 30 seconds

// Common types
export type ApiResponse<T> = {
  success: boolean;
  data?: T;
  error?: string;
};

// Error handling
const handleApiError = (error: any): string => {
  console.error("API Error:", error);

  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    return error.response.data?.message || `Error: ${error.response.status}`;
  } else if (error.request) {
    // The request was made but no response was received
    return "No response received from server. Please check your connection.";
  } else {
    // Something happened in setting up the request that triggered an Error
    return error.message || "An unknown error occurred";
  }
};

// Helper function for API calls
const apiCall = async <T>(
  url: string,
  method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
  data?: any
): Promise<ApiResponse<T>> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    const options: RequestInit = {
      method,
      headers: {
        "Content-Type": "application/json",
        // Add authorization headers if needed
        // "Authorization": `Bearer ${token}`
      },
      signal: controller.signal,
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${API_BASE_URL}${url}`, options);
    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const responseData = await response.json();
    return { success: true, data: responseData };
  } catch (error: any) {
    const errorMessage = handleApiError(error);
    return { success: false, error: errorMessage };
  }
};

// File upload helper
const uploadFile = async <T>(
  url: string,
  file: File,
  additionalData?: Record<string, any>
): Promise<ApiResponse<T>> => {
  try {
    const formData = new FormData();
    formData.append("file", file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    const response = await fetch(`${API_BASE_URL}${url}`, {
      method: "POST",
      body: formData,
      signal: controller.signal,
      // No Content-Type header needed as it's set automatically for FormData
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const responseData = await response.json();
    return { success: true, data: responseData };
  } catch (error: any) {
    const errorMessage = handleApiError(error);
    return { success: false, error: errorMessage };
  }
};

// ===== TITLE GENERATION =====

export type TitleGenerationRequest = {
  videoFile?: File;
  youtubeUrl?: string;
  keywords?: string[];
  targetAudience?: string;
  contentType?: string;
  potentialTitle?: string;
  description?: string;
  niche?: string;
};

export type TitleGenerationResponse = {
  titles: string[];
  metrics?: {
    estimatedCTR: number;
    keywordScore: number;
  }[];
};

/**
 * Generate title suggestions for a video
 * @param data Request data containing either a video file or YouTube URL
 * @returns Array of generated titles
 */
export const generateTitles = async (
  data: TitleGenerationRequest
): Promise<ApiResponse<TitleGenerationResponse>> => {
  try {
    if (!data.videoFile && !data.youtubeUrl) {
      throw new Error("Either videoFile or youtubeUrl must be provided");
    }

    if (data.videoFile) {
      return await uploadFile<TitleGenerationResponse>("/titles/generate", data.videoFile, {
        keywords: data.keywords?.join(","),
        targetAudience: data.targetAudience,
        contentType: data.contentType,
      });
    } else {
      return await apiCall<TitleGenerationResponse>("/titles/generate", "POST", {
        youtubeUrl: data.youtubeUrl,
        keywords: data.keywords,
        targetAudience: data.targetAudience,
        contentType: data.contentType,
      });
    }
  } catch (error: any) {
    const errorMessage = handleApiError(error);
    toast({
      variant: "destructive",
      title: "Title Generation Failed",
      description: errorMessage,
    });
    return { success: false, error: errorMessage };
  }
};

// ===== THUMBNAIL GENERATION =====

export type ThumbnailGenerationRequest = {
  videoFile?: File;
  youtubeUrl?: string;
  title?: string;
  style?: "modern" | "clickbait" | "minimal" | "professional";
  customText?: string;
  potentialTitle?: string;
  description?: string;
  niche?: string;
};

export type ThumbnailGenerationResponse = {
  thumbnails: string[]; // URLs to generated thumbnails
};

/**
 * Generate thumbnail suggestions for a video
 * @param data Request data containing either a video file or YouTube URL
 * @returns Array of generated thumbnail URLs
 */
export const generateThumbnails = async (
  data: ThumbnailGenerationRequest
): Promise<ApiResponse<ThumbnailGenerationResponse>> => {
  try {
    if (!data.videoFile && !data.youtubeUrl) {
      throw new Error("Either videoFile or youtubeUrl must be provided");
    }

    if (data.videoFile) {
      return await uploadFile<ThumbnailGenerationResponse>("/thumbnails/generate", data.videoFile, {
        title: data.title,
        style: data.style,
        customText: data.customText,
      });
    } else {
      return await apiCall<ThumbnailGenerationResponse>("/thumbnails/generate", "POST", {
        youtubeUrl: data.youtubeUrl,
        title: data.title,
        style: data.style,
        customText: data.customText,
      });
    }
  } catch (error: any) {
    const errorMessage = handleApiError(error);
    toast({
      variant: "destructive",
      title: "Thumbnail Generation Failed",
      description: errorMessage,
    });
    return { success: false, error: errorMessage };
  }
};

// ===== DESCRIPTION WRITING =====

export type DescriptionGenerationRequest = {
  videoFile?: File;
  youtubeUrl?: string;
  title?: string;
  keywords?: string[];
  includeTimestamps?: boolean;
  descriptionLength?: "short" | "medium" | "long";
};

export type DescriptionGenerationResponse = {
  descriptions: string[];
};

/**
 * Generate description suggestions for a video
 * @param data Request data containing either a video file or YouTube URL
 * @returns Array of generated descriptions
 */
export const generateDescriptions = async (
  data: DescriptionGenerationRequest
): Promise<ApiResponse<DescriptionGenerationResponse>> => {
  try {
    if (!data.videoFile && !data.youtubeUrl) {
      throw new Error("Either videoFile or youtubeUrl must be provided");
    }

    if (data.videoFile) {
      return await uploadFile<DescriptionGenerationResponse>("/descriptions/generate", data.videoFile, {
        title: data.title,
        keywords: data.keywords?.join(","),
        includeTimestamps: data.includeTimestamps,
        descriptionLength: data.descriptionLength,
      });
    } else {
      return await apiCall<DescriptionGenerationResponse>("/descriptions/generate", "POST", {
        youtubeUrl: data.youtubeUrl,
        title: data.title,
        keywords: data.keywords,
        includeTimestamps: data.includeTimestamps,
        descriptionLength: data.descriptionLength,
      });
    }
  } catch (error: any) {
    const errorMessage = handleApiError(error);
    toast({
      variant: "destructive",
      title: "Description Generation Failed",
      description: errorMessage,
    });
    return { success: false, error: errorMessage };
  }
};

// ===== TAG OPTIMIZATION =====

export type TagOptimizationRequest = {
  videoFile?: File;
  youtubeUrl?: string;
  title?: string;
  description?: string;
  niche?: string;
};

export type TagOptimizationResponse = {
  tags: string[];
  metrics?: {
    searchVolume: number;
    competition: number;
  }[];
};

/**
 * Generate optimized tags for a video
 * @param data Request data containing either a video file or YouTube URL
 * @returns Array of optimized tags
 */
export const optimizeTags = async (
  data: TagOptimizationRequest
): Promise<ApiResponse<TagOptimizationResponse>> => {
  try {
    if (!data.videoFile && !data.youtubeUrl && !data.title) {
      throw new Error("Either videoFile, youtubeUrl, or title must be provided");
    }

    if (data.videoFile) {
      return await uploadFile<TagOptimizationResponse>("/tags/optimize", data.videoFile, {
        title: data.title,
        description: data.description,
        niche: data.niche,
      });
    } else {
      return await apiCall<TagOptimizationResponse>("/tags/optimize", "POST", {
        youtubeUrl: data.youtubeUrl,
        title: data.title,
        description: data.description,
        niche: data.niche,
      });
    }
  } catch (error: any) {
    const errorMessage = handleApiError(error);
    toast({
      variant: "destructive",
      title: "Tag Optimization Failed",
      description: errorMessage,
    });
    return { success: false, error: errorMessage };
  }
};
