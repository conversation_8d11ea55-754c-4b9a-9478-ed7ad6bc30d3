
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Youtube } from "lucide-react";
import { useAuth } from "@/context/AuthContext";

const Navbar = () => {
  const { user, signOut } = useAuth();
  
  return (
    <header className="sticky top-0 z-40 w-full bg-white/80 backdrop-blur-md border-b">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <Youtube className="h-6 w-6 text-brand-purple" />
          <Link to="/" className="text-xl font-bold">
            <span className="gradient-text">VideoGrowth AI</span>
          </Link>
        </div>
        
        <nav className="hidden md:flex items-center gap-8">
          <Link to="/#features" className="text-sm font-medium hover:text-primary">
            Features
          </Link>
          <Link to="/#pricing" className="text-sm font-medium hover:text-primary">
            Pricing
          </Link>
          <a href="#" className="text-sm font-medium hover:text-primary">
            Blog
          </a>
          <a href="#" className="text-sm font-medium hover:text-primary">
            Contact
          </a>
        </nav>
        
        <div className="flex items-center gap-4">
          {user ? (
            <>
              <Link to="/dashboard">
                <Button variant="ghost" className="hidden md:inline-flex">
                  Dashboard
                </Button>
              </Link>
              <Button 
                onClick={() => signOut()} 
                className="bg-gradient-to-r from-brand-purple to-brand-blue hover:opacity-90"
              >
                Sign Out
              </Button>
            </>
          ) : (
            <>
              <Link to="/auth">
                <Button variant="ghost" className="hidden md:inline-flex">
                  Log In
                </Button>
              </Link>
              <Link to="/auth">
                <Button className="bg-gradient-to-r from-brand-purple to-brand-blue hover:opacity-90">
                  Sign Up
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Navbar;
