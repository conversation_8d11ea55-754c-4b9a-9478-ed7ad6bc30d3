import React, { useState } from "react";
import { toast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, Link as LinkIcon, ArrowRight, Video } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useVideo } from "@/contexts/VideoContext";

const DashboardVideoUploader = () => {
  const { videoData, setVideoData, isProcessing } = useVideo();
  const [file, setFile] = useState<File | null>(null);
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [activeTab, setActiveTab] = useState("upload");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Check if file is a video
      if (!selectedFile.type.startsWith("video/")) {
        toast({
          variant: "destructive",
          title: "Invalid file type",
          description: "Please upload a video file.",
        });
        return;
      }

      // Check if file size is under 100MB
      if (selectedFile.size > 100 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "File too large",
          description: "Please upload a video under 100MB.",
        });
        return;
      }

      setFile(selectedFile);
    }
  };

  const handleUploadSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (file) {
      setVideoData({
        type: 'file',
        data: file
      });
      toast({
        title: "Video Uploaded",
        description: "Your video is ready to use with any of the AI tools below.",
      });
    } else {
      toast({
        variant: "destructive",
        title: "No file selected",
        description: "Please select a video file to upload.",
      });
    }
  };

  const handleYoutubeSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!youtubeUrl) {
      toast({
        variant: "destructive",
        title: "No URL provided",
        description: "Please enter a YouTube video URL.",
      });
      return;
    }

    // Simple YouTube URL validation
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/;
    if (!youtubeRegex.test(youtubeUrl)) {
      toast({
        variant: "destructive",
        title: "Invalid URL",
        description: "Please enter a valid YouTube video URL.",
      });
      return;
    }

    setVideoData({
      type: 'url',
      data: youtubeUrl
    });
    toast({
      title: "YouTube URL Added",
      description: "Your YouTube video is ready to use with any of the AI tools below.",
    });
  };

  // If video is already uploaded, show the video info
  if (videoData) {
    return (
      <Card className="w-full mb-8">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5 text-primary" />
            Video Ready for Processing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-muted rounded-lg flex justify-between items-center">
              <div className="truncate">
                <p className="font-medium truncate">
                  {videoData.type === 'file'
                    ? (videoData.data as File).name
                    : videoData.data as string}
                </p>
                {videoData.type === 'file' && (
                  <p className="text-xs text-muted-foreground">
                    {((videoData.data as File).size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                )}
                {videoData.type === 'url' && (
                  <p className="text-xs text-muted-foreground">
                    YouTube URL
                  </p>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setVideoData(null)}
                disabled={isProcessing}
              >
                Change Video
              </Button>
            </div>

            {/* Show additional video details if available */}
            {(videoData.potentialTitle || videoData.description || videoData.niche) && (
              <div className="border rounded-lg p-4">
                <h3 className="text-sm font-medium mb-2">Video Details</h3>

                {videoData.potentialTitle && (
                  <div className="mb-2">
                    <p className="text-xs text-muted-foreground">Potential Title:</p>
                    <p className="text-sm">{videoData.potentialTitle}</p>
                  </div>
                )}

                {videoData.niche && (
                  <div className="mb-2">
                    <p className="text-xs text-muted-foreground">Niche:</p>
                    <p className="text-sm">{videoData.niche}</p>
                  </div>
                )}

                {videoData.description && (
                  <div>
                    <p className="text-xs text-muted-foreground">Description:</p>
                    <p className="text-sm line-clamp-3">{videoData.description}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          <p className="text-sm text-muted-foreground mt-4">
            Your video is ready to use. Select any of the AI tools below to start processing.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full mb-8">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5 text-primary" />
          Upload a Video
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="upload">Upload Video</TabsTrigger>
            <TabsTrigger value="youtube">YouTube URL</TabsTrigger>
          </TabsList>

          <TabsContent value="upload">
            <form onSubmit={handleUploadSubmit} className="space-y-6">
              <div className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => document.getElementById("dashboard-video-upload")?.click()}>
                <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-medium mb-1">Upload your video</h3>
                <p className="text-sm text-muted-foreground mb-4">Drag and drop or click to browse</p>
                <p className="text-xs text-muted-foreground">MP4, MOV, or WebM • Up to 100MB</p>

                <Input
                  id="dashboard-video-upload"
                  type="file"
                  accept="video/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
              </div>

              {file && (
                <div className="p-4 bg-muted rounded-lg flex justify-between items-center">
                  <div className="truncate">
                    <p className="font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {(file.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setFile(null)}
                  >
                    Remove
                  </Button>
                </div>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={!file}
              >
                Use This Video <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="youtube">
            <form onSubmit={handleYoutubeSubmit} className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <LinkIcon className="h-4 w-4 text-muted-foreground" />
                  <label htmlFor="dashboard-youtube-url" className="font-medium text-sm">
                    YouTube Video URL
                  </label>
                </div>
                <Input
                  id="dashboard-youtube-url"
                  placeholder="https://www.youtube.com/watch?v=..."
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Enter a public YouTube video URL
                </p>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={!youtubeUrl}
              >
                Use This Video <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DashboardVideoUploader;
