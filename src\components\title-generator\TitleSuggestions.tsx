import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, ThumbsUp, <PERSON>rk<PERSON>, RotateCcw } from "lucide-react";

type TitleSuggestionsProps = {
  titles: string[];
  onReset: () => void;
};

const TitleSuggestions = ({ titles, onReset }: TitleSuggestionsProps) => {
  const [selectedTitle, setSelectedTitle] = useState<number | null>(null);

  const copyToClipboard = (title: string) => {
    navigator.clipboard.writeText(title);
    toast({
      description: "Title copied to clipboard!",
    });
  };

  const handleTitleSelect = (index: number) => {
    setSelectedTitle(index);
    toast({
      description: "Title selected!",
      action: (
        <Button variant="outline" size="sm" onClick={() => copyToClipboard(titles[index])}>
          Copy
        </Button>
      ),
    });
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <span className="h-6 w-6 bg-brand-purple/10 rounded-full flex items-center justify-center">
            <Sparkles className="h-3 w-3 text-brand-purple" />
          </span>
          AI Generated Titles
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-4 max-h-[600px] overflow-y-auto pr-2">
          {titles.map((title, index) => (
            <div 
              key={index}
              className={`p-3 border rounded-lg ${selectedTitle === index ? 'border-brand-purple bg-brand-purple/5' : 'hover:border-brand-purple/30 hover:bg-brand-purple/5'} cursor-pointer transition-all`}
              onClick={() => handleTitleSelect(index)}
            >
              <div className="flex justify-between items-start gap-2">
                <div className="space-y-2 flex-1">
                  <p className={`font-medium text-sm ${selectedTitle === index ? 'text-brand-purple' : ''}`}>{title}</p>
                  <div className="flex flex-wrap gap-2">
                    {getRandomTags().map((tag, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex">
                  {selectedTitle === index && <ThumbsUp className="h-4 w-4 text-brand-purple mr-2" />}
                  <Button 
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      copyToClipboard(title);
                    }}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 flex justify-end">
          <Button 
            variant="outline" 
            size="sm"
            onClick={onReset}
            className="flex items-center gap-2 text-xs"
          >
            <RotateCcw className="h-3 w-3" />
            Generate More Titles
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper function to generate random tags for demonstration
const getRandomTags = () => {
  const allTags = [
    "High CTR", "Emotional", "Question", "How-to", "Listicle", 
    "Curiosity", "Action", "SEO-friendly", "Trending", "Keyword-rich"
  ];
  const shuffled = [...allTags].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, 3);
};

export default TitleSuggestions;
