/**
 * YouTube Analytics API client for fetching channel analytics data
 * 
 * This requires YouTube Analytics API and proper OAuth2 authentication
 * with analytics scopes.
 */

import { API_CONFIG } from "@/lib/config";
import { toast } from "@/components/ui/use-toast";

export interface ChannelAnalytics {
  views: number;
  watchTimeMinutes: number;
  subscribers: number;
  likes: number;
  comments: number;
  shares: number;
  estimatedRevenue?: number;
  viewsChange?: number;
  subscribersChange?: number;
}

export interface VideoAnalytics {
  videoId: string;
  title: string;
  views: number;
  watchTimeMinutes: number;
  likes: number;
  comments: number;
  shares: number;
  publishedAt: string;
  thumbnailUrl?: string;
}

export interface AnalyticsTimeframe {
  startDate: string; // YYYY-MM-DD format
  endDate: string;   // YYYY-MM-DD format
}

/**
 * Get channel analytics data for a specific timeframe
 * Requires YouTube Analytics API access and user authentication
 */
export async function getChannelAnalytics(
  accessToken: string,
  channelId: string,
  timeframe: AnalyticsTimeframe = getDefaultTimeframe()
): Promise<ChannelAnalytics | null> {
  try {
    // YouTube Analytics API endpoint
    const baseUrl = "https://youtubeanalytics.googleapis.com/v2/reports";
    const params = new URLSearchParams({
      ids: `channel==${channelId}`,
      startDate: timeframe.startDate,
      endDate: timeframe.endDate,
      metrics: "views,estimatedMinutesWatched,likes,comments,shares,subscribersGained",
      dimensions: "",
    });

    const response = await fetch(`${baseUrl}?${params}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`YouTube Analytics API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.rows || data.rows.length === 0) {
      return null;
    }

    const [views, watchTimeMinutes, likes, comments, shares, subscribers] = data.rows[0];

    // Get previous period for comparison
    const previousPeriod = getPreviousPeriod(timeframe);
    const previousData = await getChannelAnalytics(accessToken, channelId, previousPeriod);

    return {
      views: views || 0,
      watchTimeMinutes: watchTimeMinutes || 0,
      subscribers: subscribers || 0,
      likes: likes || 0,
      comments: comments || 0,
      shares: shares || 0,
      viewsChange: previousData ? calculatePercentageChange(views, previousData.views) : 0,
      subscribersChange: previousData ? calculatePercentageChange(subscribers, previousData.subscribers) : 0,
    };

  } catch (error: any) {
    console.error("Error fetching channel analytics:", error);
    toast({
      variant: "destructive",
      title: "Analytics Error",
      description: "Failed to fetch YouTube analytics data.",
    });
    return null;
  }
}

/**
 * Get video analytics for top performing videos
 */
export async function getTopVideosAnalytics(
  accessToken: string,
  channelId: string,
  maxResults: number = 10,
  timeframe: AnalyticsTimeframe = getDefaultTimeframe()
): Promise<VideoAnalytics[]> {
  try {
    // First get video IDs and basic data from Data API
    const dataApiUrl = new URL("https://www.googleapis.com/youtube/v3/search");
    dataApiUrl.searchParams.append("part", "id,snippet");
    dataApiUrl.searchParams.append("channelId", channelId);
    dataApiUrl.searchParams.append("type", "video");
    dataApiUrl.searchParams.append("order", "viewCount");
    dataApiUrl.searchParams.append("maxResults", maxResults.toString());
    dataApiUrl.searchParams.append("publishedAfter", `${timeframe.startDate}T00:00:00Z`);
    dataApiUrl.searchParams.append("publishedBefore", `${timeframe.endDate}T23:59:59Z`);
    dataApiUrl.searchParams.append("key", API_CONFIG.youtube.apiKey);

    const videosResponse = await fetch(dataApiUrl.toString());
    if (!videosResponse.ok) {
      throw new Error(`YouTube Data API error: ${videosResponse.statusText}`);
    }

    const videosData = await videosResponse.json();
    const videos = videosData.items || [];

    // Get analytics for each video
    const videoAnalytics: VideoAnalytics[] = [];
    
    for (const video of videos) {
      const analyticsUrl = "https://youtubeanalytics.googleapis.com/v2/reports";
      const params = new URLSearchParams({
        ids: `channel==${channelId}`,
        startDate: timeframe.startDate,
        endDate: timeframe.endDate,
        metrics: "views,estimatedMinutesWatched,likes,comments,shares",
        dimensions: "video",
        filters: `video==${video.id.videoId}`,
      });

      const analyticsResponse = await fetch(`${analyticsUrl}?${params}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        },
      });

      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json();
        if (analyticsData.rows && analyticsData.rows.length > 0) {
          const [videoId, views, watchTime, likes, comments, shares] = analyticsData.rows[0];
          
          videoAnalytics.push({
            videoId: video.id.videoId,
            title: video.snippet.title,
            views: views || 0,
            watchTimeMinutes: watchTime || 0,
            likes: likes || 0,
            comments: comments || 0,
            shares: shares || 0,
            publishedAt: video.snippet.publishedAt,
            thumbnailUrl: video.snippet.thumbnails?.default?.url,
          });
        }
      }
    }

    return videoAnalytics.sort((a, b) => b.views - a.views);

  } catch (error: any) {
    console.error("Error fetching video analytics:", error);
    return [];
  }
}

/**
 * Get the user's YouTube channel ID using their access token
 */
export async function getUserChannelId(accessToken: string): Promise<string | null> {
  try {
    const url = new URL("https://www.googleapis.com/youtube/v3/channels");
    url.searchParams.append("part", "id");
    url.searchParams.append("mine", "true");
    url.searchParams.append("key", API_CONFIG.youtube.apiKey);

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.items?.[0]?.id || null;

  } catch (error: any) {
    console.error("Error getting channel ID:", error);
    return null;
  }
}

// Helper functions
function getDefaultTimeframe(): AnalyticsTimeframe {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30); // Last 30 days

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
  };
}

function getPreviousPeriod(timeframe: AnalyticsTimeframe): AnalyticsTimeframe {
  const start = new Date(timeframe.startDate);
  const end = new Date(timeframe.endDate);
  const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

  const previousEnd = new Date(start);
  previousEnd.setDate(previousEnd.getDate() - 1);
  
  const previousStart = new Date(previousEnd);
  previousStart.setDate(previousStart.getDate() - daysDiff);

  return {
    startDate: previousStart.toISOString().split('T')[0],
    endDate: previousEnd.toISOString().split('T')[0],
  };
}

function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100 * 100) / 100;
}
