import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Loader2, Tag } from "lucide-react";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";

interface VideoDetails {
  title: string;
  description: string;
}

export interface TagFormData {
  title: string;
  description: string;
  competitorUrl: string;
  specificKeywords: string;
  tagCount: string;
  includeNicheTags: boolean;
  includeBrandTags: boolean;
  localLanguage: string;
}

interface TagOptimizerInputsProps {
  initialValues: VideoDetails;
  isLoading: boolean;
  onSubmit: (data: TagFormData) => void;
}

const TagOptimizerInputs: React.FC<TagOptimizerInputsProps> = ({
  initialValues,
  isLoading,
  onSubmit,
}) => {
  const [formData, setFormData] = useState<TagFormData>({
    title: initialValues.title || "",
    description: initialValues.description || "",
    competitorUrl: "",
    specificKeywords: "",
    tagCount: "15",
    includeNicheTags: true,
    includeBrandTags: true,
    localLanguage: "english"
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Two main columns for the form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Video Information */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Video Information</h3>
            
            {/* Title field */}
            <div className="space-y-2 mb-4">
              <Label htmlFor="title" className="text-sm font-medium">Video Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                placeholder="Enter your video title"
                className="bg-background/60"
                disabled={isLoading}
              />
            </div>
            
            {/* Description field */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">Video Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Enter your video description"
                className="min-h-[150px] bg-background/60"
                disabled={isLoading}
              />
            </div>
          </div>
          
          {/* External Resources */}
          <div>
            <h3 className="text-lg font-medium mb-3">External Resources</h3>
            
            {/* Competitor URL field */}
            <div className="space-y-1 mb-4">
              <Label htmlFor="competitorUrl" className="text-sm font-medium">Competitor Video URL (Optional)</Label>
              <Input
                id="competitorUrl"
                name="competitorUrl"
                value={formData.competitorUrl}
                onChange={handleChange}
                placeholder="https://youtube.com/watch?v=..."
                className="bg-background/60"
                disabled={isLoading}
              />
              <p className="text-xs text-muted-foreground">
                Enter a similar video's URL to analyze its tags
              </p>
            </div>
            
            {/* Specific Keywords field */}
            <div className="space-y-1">
              <Label htmlFor="specificKeywords" className="text-sm font-medium">Specific Keywords (Optional)</Label>
              <Input
                id="specificKeywords"
                name="specificKeywords"
                value={formData.specificKeywords}
                onChange={handleChange}
                placeholder="e.g. youtube growth, video marketing"
                className="bg-background/60"
                disabled={isLoading}
              />
              <p className="text-xs text-muted-foreground">
                Enter comma-separated keywords you want to include
              </p>
            </div>
          </div>
        </div>
        
        {/* Right Column - Tag Preferences */}
        <div>
          <h3 className="text-lg font-medium mb-4">Tag Preferences</h3>
          
          <div className="bg-muted/20 p-5 rounded-lg border border-border/50 space-y-6">
            {/* Tag Count & Language row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {/* Tag Count */}
              <div className="space-y-2">
                <Label htmlFor="tagCount" className="font-medium">Number of tags</Label>
                <Select
                  value={formData.tagCount}
                  disabled={isLoading}
                  onValueChange={(value) => handleSelectChange("tagCount", value)}
                >
                  <SelectTrigger className="bg-background/60">
                    <SelectValue placeholder="Choose number of tags" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10 tags</SelectItem>
                    <SelectItem value="15">15 tags</SelectItem>
                    <SelectItem value="20">20 tags</SelectItem>
                    <SelectItem value="30">30 tags</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Language */}
              <div className="space-y-2">
                <Label htmlFor="localLanguage" className="font-medium">Tag Language</Label>
                <Select
                  value={formData.localLanguage}
                  disabled={isLoading}
                  onValueChange={(value) => handleSelectChange("localLanguage", value)}
                >
                  <SelectTrigger className="bg-background/60">
                    <SelectValue placeholder="Choose language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="english">English</SelectItem>
                    <SelectItem value="spanish">Spanish</SelectItem>
                    <SelectItem value="french">French</SelectItem>
                    <SelectItem value="german">German</SelectItem>
                    <SelectItem value="japanese">Japanese</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* Tag Options */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Tag Options</h4>
              <div className="grid grid-cols-1 gap-4 bg-background/40 p-4 rounded-lg">
                {/* Niche Tags */}
                <div className="flex items-center justify-between">
                  <Label htmlFor="includeNicheTags" className="cursor-pointer">Include niche-specific tags</Label>
                  <Switch
                    id="includeNicheTags"
                    checked={formData.includeNicheTags}
                    onCheckedChange={(checked) => handleSwitchChange("includeNicheTags", checked)}
                    disabled={isLoading}
                  />
                </div>
                
                {/* Brand Tags */}
                <div className="flex items-center justify-between">
                  <Label htmlFor="includeBrandTags" className="cursor-pointer">Include brand-related tags</Label>
                  <Switch
                    id="includeBrandTags"
                    checked={formData.includeBrandTags}
                    onCheckedChange={(checked) => handleSwitchChange("includeBrandTags", checked)}
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>
            
            {/* YouTube Tags Tip */}
            <div className="bg-brand-purple/10 p-4 rounded-lg border border-brand-purple/20">
              <h4 className="text-sm font-medium text-brand-purple mb-1">YouTube Tags Tip</h4>
              <p className="text-xs text-muted-foreground">
                Tags help YouTube understand your content. Use a mix of specific and general tags, 
                with the most important ones first. YouTube allows up to 500 characters in total.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Submit button */}
      <Button 
        type="submit" 
        variant="default"
        className="w-full bg-brand-purple hover:bg-brand-purple/90 mt-2"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating Tags...
          </>
        ) : (
          'Generate Tags'
        )}
      </Button>
    </form>
  );
};

export default TagOptimizerInputs;
