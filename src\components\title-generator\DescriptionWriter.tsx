import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PlusCircle, Trash2, Sparkles } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";

interface DescriptionWriterProps {
  onDescriptionGenerate: (descriptionData: DescriptionData) => Promise<void>;
  isLoading: boolean;
}

export interface DescriptionData {
  videoSummary: string;
  affiliateLinks: AffiliateLink[];
  includeTimestamps: boolean;
  targetKeywords: string[];
  callToAction: string;
}

interface AffiliateLink {
  id: string;
  productName: string;
  url: string;
  description?: string;
}

const DescriptionWriter: React.FC<DescriptionWriterProps> = ({
  onDescriptionGenerate,
  isLoading
}) => {
  const [activeTab, setActiveTab] = useState<string>("basic");
  const [videoSummary, setVideoSummary] = useState<string>("");
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);
  const [includeTimestamps, setIncludeTimestamps] = useState<boolean>(true);
  const [targetKeywords, setTargetKeywords] = useState<string>("");
  const [callToAction, setCallToAction] = useState<string>("");

  // Add a new empty affiliate link
  const addAffiliateLink = () => {
    setAffiliateLinks([
      ...affiliateLinks,
      { id: crypto.randomUUID(), productName: "", url: "", description: "" }
    ]);
  };

  // Remove an affiliate link by id
  const removeAffiliateLink = (id: string) => {
    setAffiliateLinks(affiliateLinks.filter(link => link.id !== id));
  };

  // Update an affiliate link
  const updateAffiliateLink = (id: string, field: keyof AffiliateLink, value: string) => {
    setAffiliateLinks(
      affiliateLinks.map(link => 
        link.id === id ? { ...link, [field]: value } : link
      )
    );
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!videoSummary.trim()) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide a video summary."
      });
      return;
    }
    
    // Filter out incomplete affiliate links
    const validAffiliateLinks = affiliateLinks.filter(
      link => link.productName.trim() && link.url.trim()
    );
    
    // Process keywords
    const processedKeywords = targetKeywords
      .split(',')
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
      
    const descriptionData: DescriptionData = {
      videoSummary,
      affiliateLinks: validAffiliateLinks,
      includeTimestamps,
      targetKeywords: processedKeywords,
      callToAction: callToAction.trim()
    };
    
    try {
      await onDescriptionGenerate(descriptionData);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Generation failed",
        description: error.message || "Failed to generate description. Please try again."
      });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <span className="h-6 w-6 bg-brand-purple/10 rounded-full flex items-center justify-center">
            <Sparkles className="h-3 w-3 text-brand-purple" />
          </span>
          Description Writer
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="advanced">Advanced Options</TabsTrigger>
          </TabsList>
          
          <form onSubmit={handleSubmit}>
            <TabsContent value="basic" className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="video-summary">Video Summary</Label>
                <Textarea 
                  id="video-summary"
                  placeholder="Provide a detailed summary of your video content..."
                  value={videoSummary}
                  onChange={(e) => setVideoSummary(e.target.value)}
                  rows={5}
                  className="resize-none"
                />
                <p className="text-xs text-muted-foreground">
                  The more detailed your description, the better the AI can generate optimal content.
                </p>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <Label>Affiliate Links & Products</Label>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm" 
                    onClick={addAffiliateLink}
                    className="flex items-center gap-1"
                  >
                    <PlusCircle className="h-3.5 w-3.5" />
                    Add Link
                  </Button>
                </div>
                
                {affiliateLinks.length === 0 ? (
                  <div className="bg-muted/50 rounded-lg p-4 text-center">
                    <p className="text-sm text-muted-foreground">
                      No affiliate links added. Click "Add Link" to include products.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {affiliateLinks.map((link) => (
                      <div key={link.id} className="border rounded-lg p-4 space-y-3">
                        <div className="flex justify-between">
                          <Label className="text-sm font-medium">Product Details</Label>
                          <Button 
                            type="button" 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => removeAffiliateLink(link.id)}
                            className="h-7 w-7 p-0 rounded-full text-destructive hover:text-destructive/90"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="grid gap-2">
                          <Input 
                            placeholder="Product Name" 
                            value={link.productName}
                            onChange={(e) => updateAffiliateLink(link.id, 'productName', e.target.value)}
                          />
                          
                          <Input 
                            placeholder="Affiliate URL" 
                            value={link.url}
                            onChange={(e) => updateAffiliateLink(link.id, 'url', e.target.value)}
                          />
                          
                          <Textarea 
                            placeholder="Brief product description (optional)" 
                            value={link.description}
                            onChange={(e) => updateAffiliateLink(link.id, 'description', e.target.value)}
                            rows={2}
                            className="resize-none"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="advanced" className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="include-timestamps"
                    checked={includeTimestamps}
                    onChange={(e) => setIncludeTimestamps(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <Label htmlFor="include-timestamps">
                    Include Timestamps Placeholders
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground pl-6">
                  Add timestamp placeholders that you can fill in later
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="target-keywords">Target Keywords</Label>
                <Input
                  id="target-keywords"
                  placeholder="Keyword 1, Keyword 2, Keyword 3..."
                  value={targetKeywords}
                  onChange={(e) => setTargetKeywords(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Enter keywords separated by commas to target in your description
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="call-to-action">Custom Call-to-Action</Label>
                <Input
                  id="call-to-action"
                  placeholder="Subscribe for more content like this!"
                  value={callToAction}
                  onChange={(e) => setCallToAction(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Custom call-to-action to include at the end of your description
                </p>
              </div>
            </TabsContent>
            
            <div className="mt-6">
              <Button 
                type="submit" 
                className="w-full bg-brand-purple hover:bg-brand-purple/90"
                disabled={isLoading || !videoSummary.trim()}
              >
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></span>
                    Generating...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    Generate Description
                  </span>
                )}
              </Button>
            </div>
          </form>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DescriptionWriter;
