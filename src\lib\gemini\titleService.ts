import { API_CONFIG } from '../config';
import { toast } from '../../components/ui/use-toast'; // Assuming this path is correct
import { callGeminiApi } from './client'; // Assuming client.ts is in the same directory

export interface GeminiTitleGenerationRequest {
  trendingTitles: string[];
  originalTitle?: string;
  description?: string;
  niche?: string;
}

export interface GeminiTitleGenerationResponse {
  titles: string[];
}

function constructTitleGenerationPrompt(data: GeminiTitleGenerationRequest): string {
  const { trendingTitles, originalTitle, description, niche } = data;
  const trendingTitlesList = trendingTitles.map((title, index) => `${index + 1}. ${title}`).join('\n');
  
  return `
You are a YouTube title optimization expert. Your task is to create 3 high-performing, engaging YouTube titles based on trending content in the same niche.

TRENDING TITLES IN THIS NICHE:
${trendingTitlesList}

${originalTitle ? `ORIGINAL TITLE IDEA: ${originalTitle}` : ''}
${description ? `VIDEO DESCRIPTION: ${description}` : ''}
${niche ? `CONTENT NICHE: ${niche}` : ''}

Create 3 title alternatives that:
1. Are inspired by the trending titles but not direct copies
2. Use proven clickworthy patterns like numbers, questions, or emotional hooks
3. Are specific and clear (not clickbait)
4. Are under 60 characters for optimal display
5. Include relevant keywords for SEO

Format your response as:
TITLE 1: [First title suggestion]
TITLE 2: [Second title suggestion]
TITLE 3: [Third title suggestion]

Only provide the titles, no additional explanation.
`.trim();
}

function parseGeneratedTitles(text: string): string[] {
  const titleRegex = /TITLE\s*\d+\s*:\s*(.*)/gi;
  const matches = [...text.matchAll(titleRegex)];
  
  if (matches.length > 0) {
    return matches.map(match => match[1].trim());
  }
  
  const lines = text.split('\n');
  const filteredLines = lines
    .map(line => line.trim())
    .filter(line => 
      line.length > 10 && 
      line.length < 100 && 
      !line.startsWith('```') &&
      !line.startsWith('TRENDING') &&
      !line.startsWith('Create') &&
      !line.startsWith('Format')
    );
  
  return filteredLines.slice(0, 3);
}

// Removed keyword extraction, pattern generation, and fallback functions as they're no longer needed

export async function generateOptimizedTitles(
  data: GeminiTitleGenerationRequest
): Promise<GeminiTitleGenerationResponse | null> {
  try {
    const apiKey = API_CONFIG.gemini.apiKey;
    
    if (!apiKey) {
      toast({
        variant: "destructive",
        title: "Gemini API Key Not Found",
        description: "Please add your Gemini API key to the .env.local file (VITE_GEMINI_API_KEY)."
      });
      console.error("Gemini API key is missing. Please check your .env.local file.");
      return null;
    }

    if (!data.trendingTitles || data.trendingTitles.length === 0) {
      toast({
        variant: "destructive",
        title: "No Trending Titles",
        description: "Trending title data is required for optimized title generation."
      });
      return null; 
    }

    // Limit trending titles to reduce token usage
    const limitedTitles = data.trendingTitles.slice(0, 5); // Reduced from 10 to 5
    const requestData = { ...data, trendingTitles: limitedTitles };
    const prompt = constructTitleGenerationPrompt(requestData);
    
    console.log("Sending title generation request to Gemini API...");
    
    // Use gemini-1.5-flash model which is more widely supported
    const model = "gemini-1.5-flash";
    
    try {
      console.log(`Using model: ${model}`);
      
      // Use lower temperature and max tokens to reduce likelihood of rate limiting
      const options = {
        temperature: 0.6,
        maxOutputTokens: 512
      };
      
      const generatedText = await callGeminiApi(prompt, apiKey, model, options);
      console.log(`Raw Gemini response from ${model}:`, generatedText);
      
      const titles = parseGeneratedTitles(generatedText);
      
      if (titles.length === 0) {
        throw new Error("No titles were generated by API");
      }
      
      return { titles };
    } catch (error: any) {
      console.error(`Error calling Gemini API for titles: ${error.message}`);
      
      // If the error is related to rate limiting, show a more specific message
      const isRateLimitError = error.message.includes("429") || 
                               error.message.includes("rate limit") || 
                               error.message.includes("quota");
      
      toast({
        variant: "destructive",
        title: isRateLimitError ? "API Rate Limit Reached" : "Title Generation Failed",
        description: error.message || "Failed to generate titles. Please try again later."
      });
      
      return null;
    }
  } catch (error: any) {
    console.error(`Title generation error: ${error.message}`);
    toast({
      variant: "destructive",
      title: "Title Generation Failed",
      description: error.message || "Failed to generate titles. Please try again."
    });
    return null;
  }
}
