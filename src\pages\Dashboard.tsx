import React from "react";
import Sidebar from "@/components/dashboard/Sidebar";
import Header from "@/components/dashboard/Header";
import { VideoProvider } from "@/contexts/VideoContext";
import DashboardOverview from "@/components/dashboard/overview/DashboardOverview";

// Inner component to use useVideo hook
const DashboardContent = () => {
  return (    <div className="min-h-screen flex flex-col lg:flex-row bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      {/* Sidebar - Now with fixed width and no overlap */}
      <div className="hidden lg:block lg:w-64 lg:flex-shrink-0">
        <Sidebar />
      </div>
      <div className="lg:hidden">
        <Sidebar />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col lg:pt-0 pt-16">
        <Header
          title="Dashboard"
          subtitle="Welcome to VideoGrowth AI"
        />

        <main className="flex-1 p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            {/* Dashboard Overview with all the components */}
            <DashboardOverview />
          </div>
        </main>
      </div>
    </div>
  );
};

// Wrap with VideoProvider at the top level
const Dashboard = () => {
  return (
    <VideoProvider>
      <DashboardContent />
    </VideoProvider>
  );
};

export default Dashboard;
