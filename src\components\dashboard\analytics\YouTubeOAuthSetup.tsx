import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Youtube, ExternalLink, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

const YouTubeOAuthSetup = () => {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleGoogleOAuth = async () => {
    setIsConnecting(true);
    
    try {
      // This is where you'd implement the actual OAuth flow
      // For now, we'll show instructions
      toast({
        title: "OAuth Setup Required",
        description: "Please follow the setup instructions to enable YouTube Analytics.",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Connection Failed",
        description: "Failed to connect to YouTube. Please try again.",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Youtube className="h-5 w-5 text-red-600" />
            Connect YouTube Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            To display real YouTube analytics data, you need to connect your YouTube channel. 
            This requires setting up OAuth2 authentication with the YouTube Analytics API.
          </p>
          
          <Button 
            onClick={handleGoogleOAuth}
            disabled={isConnecting}
            className="w-full bg-red-600 hover:bg-red-700"
          >
            <Youtube className="mr-2 h-4 w-4" />
            {isConnecting ? "Connecting..." : "Connect YouTube Channel"}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-amber-600" />
            Setup Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div>
                <h4 className="font-medium">Create Google Cloud Project</h4>
                <p className="text-sm text-muted-foreground">
                  Go to the Google Cloud Console and create a new project or select an existing one.
                </p>
                <Button variant="link" className="p-0 h-auto text-blue-600" asChild>
                  <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer">
                    Google Cloud Console <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </Button>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div>
                <h4 className="font-medium">Enable YouTube APIs</h4>
                <p className="text-sm text-muted-foreground">
                  Enable both YouTube Data API v3 and YouTube Analytics API in your project.
                </p>
                <div className="space-y-1 mt-2">
                  <Button variant="link" className="p-0 h-auto text-blue-600 block" asChild>
                    <a href="https://console.cloud.google.com/apis/library/youtube.googleapis.com" target="_blank" rel="noopener noreferrer">
                      YouTube Data API v3 <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </Button>
                  <Button variant="link" className="p-0 h-auto text-blue-600 block" asChild>
                    <a href="https://console.cloud.google.com/apis/library/youtubeanalytics.googleapis.com" target="_blank" rel="noopener noreferrer">
                      YouTube Analytics API <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div>
                <h4 className="font-medium">Create OAuth 2.0 Credentials</h4>
                <p className="text-sm text-muted-foreground">
                  Create OAuth 2.0 client ID credentials for a web application.
                </p>
                <Button variant="link" className="p-0 h-auto text-blue-600" asChild>
                  <a href="https://console.cloud.google.com/apis/credentials" target="_blank" rel="noopener noreferrer">
                    API Credentials <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </Button>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                4
              </div>
              <div>
                <h4 className="font-medium">Configure OAuth Scopes</h4>
                <p className="text-sm text-muted-foreground">
                  Add the following OAuth scopes to your application:
                </p>
                <div className="bg-gray-100 p-2 rounded text-xs font-mono mt-2">
                  <div>https://www.googleapis.com/auth/youtube.readonly</div>
                  <div>https://www.googleapis.com/auth/yt-analytics.readonly</div>
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                5
              </div>
              <div>
                <h4 className="font-medium">Update Environment Variables</h4>
                <p className="text-sm text-muted-foreground">
                  Add your OAuth credentials to your environment variables:
                </p>
                <div className="bg-gray-100 p-2 rounded text-xs font-mono mt-2">
                  <div>VITE_GOOGLE_CLIENT_ID=your_client_id</div>
                  <div>VITE_GOOGLE_CLIENT_SECRET=your_client_secret</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Implementation Notes
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm space-y-2">
            <p>
              <strong>OAuth Flow:</strong> The app will redirect users to Google's OAuth consent screen, 
              then handle the callback to receive access and refresh tokens.
            </p>
            <p>
              <strong>Data Storage:</strong> User tokens will be securely stored in Supabase with encryption.
            </p>
            <p>
              <strong>Data Sync:</strong> Analytics data will be fetched periodically and cached in the database 
              for better performance and offline access.
            </p>
            <p>
              <strong>Rate Limits:</strong> The app will respect YouTube API rate limits and implement 
              proper error handling for quota exceeded scenarios.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default YouTubeOAuthSetup;
