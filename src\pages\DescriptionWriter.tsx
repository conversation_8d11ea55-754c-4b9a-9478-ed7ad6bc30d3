import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Header from "@/components/dashboard/Header";
import Sidebar from "@/components/dashboard/Sidebar";
import DescriptionWriter from "@/components/description-writer/DescriptionWriter";
import GeneratedDescription from "@/components/description-writer/GeneratedDescription";
import GeneratedDescriptionPlaceholder from "@/components/description-writer/GeneratedDescriptionPlaceholder";
import { useVideo, VideoProvider } from "@/contexts/VideoContext";
import { toast } from "@/components/ui/use-toast";
import { generateDescription, DescriptionGenerationRequest } from "@/lib/geminiApi";
import { DescriptionData } from "@/components/description-writer/DescriptionWriter";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

// Inner component to use the useVideo hook
const DescriptionWriterContent = () => {
  const { videoData } = useVideo();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [generatedDescription, setGeneratedDescription] = useState<string>("");
  // Check if we have video data from context or location state
  React.useEffect(() => {
    if (!videoData && !location.state?.videoData) {
      toast({
        variant: "destructive",
        title: "No video selected",
        description: "Please select a video first.",
      });
      navigate("/ai-features");
    }
  }, [videoData, navigate, location.state]);const handleBack = () => {
    // Pass video data from either context or location state when navigating back
    navigate('/ai-features', { state: { videoData: videoData || location.state?.videoData } });
  };

  const handleGenerateDescription = async (descriptionData: DescriptionData) => {
    setIsLoading(true);
    try {
      // Get video information from context
      const videoInfo = videoData?.niche || "general";
      
      // Convert from component interface to API interface
      const apiRequest: DescriptionGenerationRequest = {
        videoSummary: descriptionData.videoSummary,
        affiliateLinks: descriptionData.affiliateLinks,
        includeTimestamps: descriptionData.includeTimestamps,
        targetKeywords: descriptionData.targetKeywords,
        callToAction: descriptionData.callToAction,
        niche: videoInfo
      };
      
      // Generate description using the API
      const response = await generateDescription(apiRequest);
      if (response) {
        setGeneratedDescription(response.description);
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error generating description",
        description: error.message || "Failed to generate description. Please try again.",
      });
      console.error("Error generating description:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetDescription = () => {
    setGeneratedDescription("");
  };
  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <div className="w-64 hidden lg:block">
        <Sidebar />
      </div>
      <div className="lg:hidden">
        <Sidebar />
      </div>

      <div className="flex-1 flex flex-col lg:pt-0 pt-16">
        <Header
          title="Description Writer"
          subtitle="Generate SEO-friendly descriptions for your videos"
        />        
        <main className="flex-1 p-4 md:p-8">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Back button */}
            <Button 
              variant="outline"
              size="sm"
              onClick={handleBack}
              className="mb-4 flex items-center gap-1"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>

            {!generatedDescription ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="lg:col-span-1">
                  <DescriptionWriter 
                    onDescriptionGenerate={handleGenerateDescription}
                    isLoading={isLoading}
                  />
                </div>
                <div className="lg:col-span-1">
                  <GeneratedDescriptionPlaceholder isLoading={isLoading} />
                </div>
              </div>
            ) : (
              <GeneratedDescription
                description={generatedDescription}
                onReset={resetDescription}
              />
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

// Wrap with VideoProvider at the top level
const DescriptionWriterPage = () => {
  return (
    <VideoProvider>
      <DescriptionWriterContent />
    </VideoProvider>
  );
};

export default DescriptionWriterPage;