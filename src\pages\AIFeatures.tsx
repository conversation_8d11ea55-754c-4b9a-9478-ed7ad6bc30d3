import React, { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import Sidebar from "@/components/dashboard/Sidebar";
import Header from "@/components/dashboard/Header";
import DashboardCard from "@/components/dashboard/DashboardCard";
import { VideoProvider, useVideo } from "@/contexts/VideoContext";
import { toast } from "@/components/ui/use-toast";
import { Video, FileCheck } from "lucide-react";

// Inner component to use the useVideo hook
const AIFeaturesContent = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { videoData, setVideoData } = useVideo();
  
  // Check if we have video data from state or context
  useEffect(() => {
    // If we have video data from the location state, set it in context
    if (location.state?.videoData && !videoData) {
      setVideoData(location.state.videoData);
    } 
    // If no video data is available, redirect back to dashboard
    else if (!videoData) {
      toast({
        variant: "destructive",
        title: "No video selected",
        description: "Please upload a video before accessing AI features.",
      });
      navigate("/dashboard");
    }
  }, [location.state, videoData, setVideoData, navigate]);

  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <div className="w-64 hidden lg:block">
        <Sidebar />
      </div>
      <div className="lg:hidden">
        <Sidebar />
      </div>

      <div className="flex-1 flex flex-col lg:pt-0 pt-16">
        <Header
          title="AI Features"
          subtitle="Enhance your video with our AI-powered tools"
        />
        <main className="flex-1 p-4 md:p-8">
          <div className="max-w-6xl mx-auto">
            {/* Video Info Banner */}
            {videoData && (
              <div className="bg-gradient-to-r from-brand-purple/10 to-brand-blue/10 rounded-xl p-4 mb-8 border border-brand-purple/20 flex items-center gap-4">
                <div className="h-12 w-12 bg-brand-purple/20 rounded-full flex items-center justify-center">
                  <Video className="h-6 w-6 text-brand-purple" />
                </div>
                <div>
                  <h3 className="font-medium text-brand-purple">Video Ready</h3>
                  <p className="text-sm text-muted-foreground">
                    {videoData.type === 'file' 
                      ? `Uploaded: ${(videoData.data as File).name}`
                      : `YouTube URL: ${videoData.data as string}`
                    } | Niche: {videoData.niche}
                  </p>
                </div>
              </div>
            )}
            
            {/* AI Features Section */}
            <div className="mb-10">
              <div className="flex items-center justify-center gap-2 mb-8">
                <FileCheck className="h-6 w-6 text-brand-purple" />
                <h2 className="text-3xl font-bold text-center">Choose a Feature for Your Video</h2>
              </div>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <DashboardCard
                  title="Title Generator"
                  description="Generate high-CTR titles for your videos using our advanced AI."
                  icon="video"
                  linkText="Create Titles"
                  linkUrl="/title-generator"
                  actionType="title"
                />
                <DashboardCard
                  title="Thumbnail Creator"
                  description="Create eye-catching thumbnails that stand out and drive more clicks."
                  icon="image"
                  linkText="Design Thumbnails"
                  linkUrl="/thumbnail-creator"
                  actionType="thumbnail"
                />
                <DashboardCard
                  title="Description Writer"
                  description="Generate SEO-friendly descriptions that help your videos rank higher."
                  icon="file-text"
                  linkText="Write Descriptions"
                  linkUrl="/description-writer"
                  actionType="description"
                />                <DashboardCard
                  title="Tag Optimizer"
                  description="Find the perfect tags to improve discoverability and reach."
                  icon="tag"
                  linkText="Optimize Tags"
                  linkUrl="/tag-optimizer"
                  actionType="tag"
                />
                <DashboardCard
                  title="Analytics"
                  description="View detailed analytics and insights about your channel performance."
                  icon="bar-chart"
                  linkText="View Analytics"
                  linkUrl="/analytics"
                  disabled
                />
                <DashboardCard
                  title="Competitor Research"
                  description="Analyze successful channels in your niche to refine your strategy."
                  icon="search"
                  linkText="Research Competitors"
                  linkUrl="/competitor-research"
                  disabled
                />
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

// Wrap with VideoProvider at the top level
const AIFeatures = () => {
  return (
    <VideoProvider>
      <AIFeaturesContent />
    </VideoProvider>
  );
};

export default AIFeatures;
