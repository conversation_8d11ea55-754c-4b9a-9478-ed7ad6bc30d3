import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { Check, Clipboard, Copy, ThumbsUp, X } from "lucide-react";

interface TagSuggestionsProps {
  tags: string[];
}

const TagSuggestions: React.FC<TagSuggestionsProps> = ({ tags }) => {
  const [selectedTags, setSelectedTags] = useState<string[]>(tags);
  const [copied, setCopied] = useState(false);

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag) 
        : [...prev, tag]
    );
  };

  const copyToClipboard = () => {
    const tagsString = selectedTags.join(', ');
    navigator.clipboard.writeText(tagsString)
      .then(() => {
        setCopied(true);
        toast({
          title: "Tags copied!",
          description: "Tags have been copied to clipboard",
        });
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => {
        toast({
          variant: "destructive",
          title: "Failed to copy",
          description: "Please try again or copy manually",
        });
        console.error('Failed to copy tags: ', err);
      });
  };
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="px-3 py-1 bg-brand-blue/10 text-brand-blue border-brand-blue/20">
            {selectedTags.length} Selected
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={copyToClipboard}
          >
            {copied ? (
              <>
                <Check className="h-3 w-3" />
                Copied
              </>
            ) : (
              <>
                <Copy className="h-3 w-3" />
                Copy Tags
              </>
            )}
          </Button>
        </div>
      </div>
      
      {/* Tags display */}
      <div className="flex flex-wrap gap-2 min-h-[160px]">
        {tags.map((tag, index) => (
          <Badge
            key={index}
            variant={selectedTags.includes(tag) ? "default" : "outline"}
            className={`
              cursor-pointer px-3 py-1.5 text-sm font-medium
              ${selectedTags.includes(tag) 
                ? 'bg-brand-purple hover:bg-brand-purple/90' 
                : 'text-muted-foreground hover:bg-muted'}
              transition-all
            `}
            onClick={() => toggleTag(tag)}
          >
            {selectedTags.includes(tag) && (
              <Check className="mr-1 h-3 w-3" />
            )}
            {tag}
            {selectedTags.includes(tag) && (
              <X 
                className="ml-1 h-3 w-3 opacity-50 hover:opacity-100" 
                onClick={(e) => {
                  e.stopPropagation();
                  toggleTag(tag);
                }}
              />
            )}
          </Badge>
        ))}
      </div>
      
      {/* Tag info bar */}
      <div className="bg-muted/20 rounded-lg p-4 border border-border/50">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <p className="text-sm text-muted-foreground">
              Click tags to select/deselect. Selected tags will be copied together.
            </p>
          </div>
          
          <div className="flex items-center gap-1.5 bg-background/80 px-3 py-1.5 rounded-full">
            <span className="text-sm text-muted-foreground">Character count:</span>
            <span className={`text-sm font-medium ${selectedTags.join(', ').length > 400 ? 'text-destructive' : 'text-brand-blue'}`}>
              {selectedTags.join(', ').length} / 500
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TagSuggestions;
