# API Key Setup Instructions

This document provides instructions for setting up API keys required by the VideoGrowth AI application, with specific focus on handling Gemini API rate limits.

## Required API Keys

1. **Google Gemini API Key** - For AI title and description generation
2. **YouTube API Key** - For YouTube data integration

## Setup Steps

### 1. Create Environment File

Create a file named `.env.local` in the root of your project with the following structure:

```
# YouTube API Key
VITE_YOUTUBE_API_KEY=your_youtube_api_key_here

# Primary Gemini API Key
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Multiple Gemini API Keys for Rate Limit Management
VITE_GEMINI_ALT_API_KEYS=second_key,third_key,fourth_key

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

> **IMPORTANT**: When adding API keys, ensure that:
> - The primary key (`VITE_GEMINI_API_KEY`) contains only a single API key without commas
> - Multiple keys should be placed in the `VITE_GEMINI_ALT_API_KEYS` variable, separated by commas
> - There should be no spaces between the keys and commas
> - Example: `VITE_GEMINI_ALT_API_KEYS=key1,key2,key3`

### 2. Obtain Google Gemini API Key(s)

1. Go to [Google AI Studio](https://ai.google.dev/)
2. Create an account or sign in with your Google account
3. Navigate to the API section
4. Create a new API key
5. Copy the key to your `.env.local` file as `VITE_GEMINI_API_KEY`
6. (Optional) Create additional API keys and add them as comma-separated values to `VITE_GEMINI_ALT_API_KEYS`

### 3. Obtain YouTube API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project
3. Enable the YouTube Data API v3
4. Create credentials (API key)
5. Copy the key to your `.env.local` file as `VITE_YOUTUBE_API_KEY`

### 4. Restart Development Server

After setting up your `.env.local` file, restart your development server:

```bash
npm run dev
```

## Understanding Gemini API Rate Limits

The error message `429 RESOURCE_EXHAUSTED` indicates that you've exceeded your quota for the Gemini API:

```
You exceeded your current quota, please check your plan and billing details.
```

This can happen for several reasons:
- Too many requests per minute
- Too many tokens (input text) per minute
- Daily request quota exceeded

## Solution Options for Rate Limiting

### Option 1: Configure Multiple API Keys

You can set up multiple Gemini API keys to distribute the load:

1. Visit [Google AI Studio](https://ai.google.dev/) to create additional API keys
2. Add your API keys to the `.env.local` file:

```
VITE_GEMINI_API_KEY=your_main_api_key
VITE_GEMINI_ALT_API_KEYS=second_key,third_key,fourth_key
```

The application will automatically rotate between these keys when rate limits are encountered.

### Option 2: Upgrade to a Paid Tier

For production use, consider upgrading to a paid tier of Google's Gemini API:
- Visit [Google AI Studio Pricing](https://ai.google.dev/pricing)
- Higher tiers provide significantly higher rate limits

### Option 3: Adjust Usage Patterns

The application includes several features to reduce API usage:
- Smart rate limiting that spaces out requests
- Fallback to smaller models when possible
- Automatic retries with exponential backoff
- Local fallback generation when API is unavailable

## API Request Optimization

The application has been optimized to efficiently use your API keys and minimize unnecessary requests:

1. **Primary Key First Approach**: 
   - The system always tries the primary API key first (`VITE_GEMINI_API_KEY`)
   - Alternative keys are only used if the primary key fails or hits rate limits
   - This ensures your primary key is prioritized and used most efficiently

2. **Model Optimization**:
   - The application uses only the `gemini-pro` model for all requests
   - This model provides good results while being more efficient with token usage
   - Token limits and parameter settings are optimized to maximize usage efficiency

3. **Smart Fallback System**:
   - If all API keys fail or hit rate limits, the application falls back to local generation
   - You'll see a toast notification when this happens
   - The fallback system ensures your application continues to work even when API access is limited

## API Key Management Best Practices

- **Never commit API keys to your code repository**
- Use different API keys for development and production
- Monitor your API usage to avoid unexpected charges
- Rotate keys periodically for security

## Technical Details

### Rate Limit Configuration

You can adjust the rate limiting parameters in `src/lib/config.ts`:

```typescript
gemini: {
  rateLimits: {
    requestsPerMinute: 5,      // Adjust based on your plan
    tokensPerMinute: 60000,    // Adjust based on your plan
    maxRetries: 3,             // Number of retry attempts
    retryDelayMs: 1000,        // Base delay between retries
  }
}
```

## Troubleshooting

- If you see "API Key Not Found" errors, verify your `.env.local` file contains the correct keys
- For rate limit errors (429), consider creating multiple Gemini API keys
- Check the application logs (browser console) for detailed error information
- If using multiple API keys but still seeing rate limits, you may need to create more keys or reduce usage

## Additional Configuration

For advanced configuration options, see the `src/lib/config.ts` file, where you can adjust:

- Rate limiting parameters
- Model settings
- Default API request values
