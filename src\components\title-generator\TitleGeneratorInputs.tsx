import React from "react";
import { NICHE_TO_CATEGORY_MAP } from "@/lib/config";

interface VideoDetails {
  title: string;
  description: string;
}

interface TitleGeneratorInputsProps {
  videoNiche: string;
  videoDetails: VideoDetails;
  onNicheChange: (niche: string) => void;
  onDetailsChange: (details: VideoDetails) => void;
}

const TitleGeneratorInputs: React.FC<TitleGeneratorInputsProps> = ({
  videoNiche,
  videoDetails,
  onNicheChange,
  onDetailsChange,
}) => {
  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 mb-6">
      <div className="bg-white rounded-lg shadow-sm p-4 border">
        <label className="block text-sm font-medium text-gray-700 mb-1">Content Niche</label>
        <select
          value={videoNiche}
          onChange={(e) => onNicheChange(e.target.value)}
          className="w-full p-2 border rounded-md bg-background"
        >
          {Object.keys(NICHE_TO_CATEGORY_MAP).map((niche) => (
            <option key={niche} value={niche}>
              {niche}
            </option>
          ))}
        </select>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-4 border">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Your Video Title (Optional)
        </label>
        <input
          type="text"
          value={videoDetails.title}
          onChange={(e) =>
            onDetailsChange({ ...videoDetails, title: e.target.value })
          }
          placeholder="Enter your draft title..."
          className="w-full p-2 border rounded-md bg-background"
        />
      </div>

      <div className="bg-white rounded-lg shadow-sm p-4 border">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Video Description (Optional)
        </label>
        <textarea
          value={videoDetails.description}
          onChange={(e) =>
            onDetailsChange({ ...videoDetails, description: e.target.value })
          }
          placeholder="Brief description of your video content..."
          className="w-full p-2 border rounded-md bg-background"
          rows={1}
        />
      </div>
    </div>
  );
};

export default TitleGeneratorInputs;
