import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Co<PERSON>, Eye, Calendar } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface TrendingTitlesListProps {
  titles: {
    id?: string;
    title: string;
    channelTitle?: string;
    viewCount?: number;
    publishedAt?: string;
  }[];
  isLoading: boolean;
}

const TrendingTitlesList: React.FC<TrendingTitlesListProps> = ({ titles, isLoading }) => {
  const copyToClipboard = (title: string) => {
    navigator.clipboard.writeText(title);
    toast({
      description: "Title copied to clipboard!",
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
  };

  const formatViews = (views?: number) => {
    if (!views) return 'N/A';
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M views`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K views`;
    } else {
      return `${views} views`;
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <span className="h-6 w-6 bg-brand-purple/10 rounded-full flex items-center justify-center">
            <span className="text-brand-purple text-xs font-bold">#</span>
          </span>
          Trending Titles
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin h-8 w-8 border-4 border-brand-purple/20 rounded-full border-t-brand-purple"></div>
          </div>
        ) : titles.length > 0 ? (
          <div className="space-y-4 max-h-[600px] overflow-y-auto pr-2">
            {titles.map((title, index) => (
              <div 
                key={title.id || index} 
                className="p-3 border rounded-lg hover:border-brand-purple/30 hover:bg-brand-purple/5 transition-all cursor-pointer"
              >
                <div className="flex justify-between items-start gap-2">
                  <div className="space-y-1 flex-1">
                    <p className="font-medium text-sm">{title.title}</p>
                    {title.channelTitle && (
                      <p className="text-xs text-muted-foreground">
                        {title.channelTitle}
                      </p>
                    )}
                    
                    <div className="flex flex-wrap gap-2 mt-2">
                      {title.viewCount && (
                        <Badge variant="outline" className="flex items-center gap-1 text-xs">
                          <Eye className="h-3 w-3" />
                          {formatViews(title.viewCount)}
                        </Badge>
                      )}
                      
                      {title.publishedAt && (
                        <Badge variant="outline" className="flex items-center gap-1 text-xs">
                          <Calendar className="h-3 w-3" />
                          {formatDate(title.publishedAt)}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    onClick={() => copyToClipboard(title.title)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-sm text-muted-foreground">
            <p>No trending titles available</p>
            <p className="mt-1">Select a niche to see trending titles</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TrendingTitlesList;
