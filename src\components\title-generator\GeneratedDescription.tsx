import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { FileText, Co<PERSON>, <PERSON>ota<PERSON><PERSON>cw, ChevronRight } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface GeneratedDescriptionProps {
  description: string;
  onReset: () => void;
}

const GeneratedDescription: React.FC<GeneratedDescriptionProps> = ({
  description,
  onReset
}) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(description);
    toast({
      description: "Description copied to clipboard!",
    });
  };

  const sections = [
    {
      title: "Video Description",
      content: description
    }
  ];

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <span className="h-6 w-6 bg-brand-purple/10 rounded-full flex items-center justify-center">
            <FileText className="h-3 w-3 text-brand-purple" />
          </span>
          Generated Description
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6 space-y-5">
        <div className="space-y-4 max-h-[600px] overflow-y-auto pr-2">
          {sections.map((section, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <ChevronRight className="h-4 w-4" />
                {section.title}
              </div>
              
              <div className="relative group">
                <Textarea
                  value={section.content}
                  readOnly
                  rows={15}
                  className="resize-none bg-muted/50 w-full"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={copyToClipboard}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-between">
          <Button 
            variant="outline" 
            size="sm"
            onClick={onReset}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-3 w-3" />
            Generate New
          </Button>
          
          <Button 
            onClick={copyToClipboard}
            className="flex items-center gap-2"
          >
            <Copy className="h-4 w-4" />
            Copy All
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default GeneratedDescription;
