import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  TrendingUp, 
  Users, 
  Clock, 
  ThumbsUp,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Youtube,
  Settings
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/components/ui/use-toast";
import { getAggregatedAnalytics, getUserYouTubeChannels } from "@/lib/supabaseAnalytics";
import { ChannelAnalytics } from "@/lib/youtubeAnalytics";

const RealAnalyticsSummary = () => {
  const [analytics, setAnalytics] = useState<ChannelAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasConnectedChannel, setHasConnectedChannel] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      // Check if user has connected YouTube channels
      const channels = await getUserYouTubeChannels();
      setHasConnectedChannel(channels.length > 0);
      
      if (channels.length > 0) {
        // Get aggregated analytics for the last 30 days
        const data = await getAggregatedAnalytics(30);
        setAnalytics(data);
      }
    } catch (error) {
      console.error("Error loading analytics:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load analytics data.",
      });
    } finally {
      setLoading(false);
    }
  };

  const connectYouTubeChannel = () => {
    // This would trigger the YouTube OAuth flow
    toast({
      title: "Connect YouTube Channel",
      description: "YouTube OAuth integration needs to be set up. See implementation steps below.",
    });
  };

  // Mock data fallback when no real data is available
  const mockData = {
    views: { value: 24580, change: 12.5, positive: true },
    watchTime: { value: 1250, change: 8.3, positive: true },
    subscribers: { value: 1842, change: 3.7, positive: true },
    likes: { value: 3250, change: -2.1, positive: false }
  };

  const getDisplayData = () => {
    if (analytics) {
      return {
        views: {
          value: analytics.views,
          change: analytics.viewsChange || 0,
          positive: (analytics.viewsChange || 0) >= 0
        },
        watchTime: {
          value: Math.round(analytics.watchTimeMinutes / 60), // Convert to hours
          change: 0, // We'd need to calculate this from historical data
          positive: true
        },
        subscribers: {
          value: analytics.subscribers,
          change: analytics.subscribersChange || 0,
          positive: (analytics.subscribersChange || 0) >= 0
        },
        likes: {
          value: analytics.likes,
          change: 0, // We'd need to calculate this from historical data
          positive: true
        }
      };
    }
    return mockData;
  };

  const displayData = getDisplayData();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!hasConnectedChannel) {
    return (
      <div className="space-y-6">
        {/* Connect YouTube Channel Card */}
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-8 text-center">
            <Youtube className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Connect Your YouTube Channel</h3>
            <p className="text-muted-foreground mb-4">
              Connect your YouTube channel to view real analytics data including views, 
              watch time, subscribers, and engagement metrics.
            </p>
            <Button 
              onClick={connectYouTubeChannel}
              className="bg-red-600 hover:bg-red-700 flex items-center gap-2"
            >
              <Youtube className="h-4 w-4" />
              Connect YouTube Channel
            </Button>
          </CardContent>
        </Card>
        
        {/* Mock Data Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Demo Mode</span>
          </div>
          <p className="text-sm text-blue-700">
            The analytics below are sample data. Connect your YouTube channel to see real metrics.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Data Source Indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 px-3 py-1 rounded-md">
          <Youtube className="h-3 w-3" />
          <span>Live YouTube Data</span>
        </div>
        <Button 
          variant="outline" 
          size="sm"
          onClick={loadAnalytics}
          disabled={loading}
        >
          Refresh Data
        </Button>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <AnalyticsCard
          title="Total Views"
          value={displayData.views.value.toLocaleString()}
          change={displayData.views.change}
          isPositive={displayData.views.positive}
          icon={<Eye className="h-5 w-5" />}
          color="blue"
        />
        <AnalyticsCard
          title="Watch Time"
          value={`${displayData.watchTime.value.toLocaleString()} hrs`}
          change={displayData.watchTime.change}
          isPositive={displayData.watchTime.positive}
          icon={<Clock className="h-5 w-5" />}
          color="purple"
        />
        <AnalyticsCard
          title="Subscribers"
          value={displayData.subscribers.value.toLocaleString()}
          change={displayData.subscribers.change}
          isPositive={displayData.subscribers.positive}
          icon={<Users className="h-5 w-5" />}
          color="green"
        />
        <AnalyticsCard
          title="Total Likes"
          value={displayData.likes.value.toLocaleString()}
          change={displayData.likes.change}
          isPositive={displayData.likes.positive}
          icon={<ThumbsUp className="h-5 w-5" />}
          color="amber"
        />
      </div>
      
      {/* Growth Chart */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="font-medium mb-1">Monthly Growth</h3>
              <p className="text-sm text-muted-foreground">Channel performance over time</p>
            </div>
            <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 px-2 py-1 rounded-md">
              <TrendingUp className="h-3 w-3" />
              <span>+{displayData.views.change}% growth</span>
            </div>
          </div>
          
          {/* Progress bars showing relative performance */}
          <div className="space-y-3">
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Views</span>
                <span className="text-muted-foreground">{displayData.views.value.toLocaleString()}</span>
              </div>
              <Progress value={85} className="h-2 bg-blue-100" />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Watch Time</span>
                <span className="text-muted-foreground">{displayData.watchTime.value.toLocaleString()} hrs</span>
              </div>
              <Progress value={65} className="h-2 bg-purple-100" />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Subscribers</span>
                <span className="text-muted-foreground">{displayData.subscribers.value.toLocaleString()}</span>
              </div>
              <Progress value={42} className="h-2 bg-green-100" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface AnalyticsCardProps {
  title: string;
  value: string;
  change: number;
  isPositive: boolean;
  icon: React.ReactNode;
  color: "blue" | "purple" | "green" | "amber";
}

const AnalyticsCard = ({ title, value, change, isPositive, icon, color }: AnalyticsCardProps) => {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-600",
    purple: "bg-purple-50 text-purple-600",
    green: "bg-green-50 text-green-600",
    amber: "bg-amber-50 text-amber-600"
  };
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-2">
          <div className={`${colorClasses[color]} p-2 rounded-md`}>
            {icon}
          </div>
          <div className={`flex items-center gap-1 text-xs ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {isPositive ? <ArrowUpRight className="h-3 w-3" /> : <ArrowDownRight className="h-3 w-3" />}
            <span>{Math.abs(change)}%</span>
          </div>
        </div>
        <h3 className="text-2xl font-bold mb-1">{value}</h3>
        <p className="text-sm text-muted-foreground">{title}</p>
      </CardContent>
    </Card>
  );
};

export default RealAnalyticsSummary;
