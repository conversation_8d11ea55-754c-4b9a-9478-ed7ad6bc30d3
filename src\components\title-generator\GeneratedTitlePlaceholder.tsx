import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Spark<PERSON> } from "lucide-react";

interface GeneratedTitlePlaceholderProps {
  isLoading: boolean;
}

const GeneratedTitlePlaceholder: React.FC<GeneratedTitlePlaceholderProps> = ({ isLoading }) => {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <span className="h-6 w-6 bg-brand-purple/10 rounded-full flex items-center justify-center">
            <Sparkles className="h-3 w-3 text-brand-purple" />
          </span>
          AI Generated Titles
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse h-16 bg-muted rounded-lg"></div>
            <div className="animate-pulse h-16 bg-muted rounded-lg"></div>
            <div className="animate-pulse h-16 bg-muted rounded-lg"></div>
          </div>
        ) : (
          <div className="h-[400px] flex flex-col items-center justify-center text-center">
            <div className="h-16 w-16 bg-brand-purple/10 rounded-full flex items-center justify-center mb-4">
              <Sparkles className="h-8 w-8 text-brand-purple" />
            </div>
            <h3 className="font-semibold text-lg mb-2">AI Title Generation</h3>
            <p className="text-muted-foreground max-w-sm">
              Click on "Generate AI Titles" to create optimized titles based on trending content in your niche.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GeneratedTitlePlaceholder;
