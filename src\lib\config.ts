/**
 * Configuration file for API keys and other settings
 * 
 * This file centralizes all API keys and configuration settings
 * for easier management and maintenance.
 */

interface ApiConfig {
  youtube: {
    apiKey: string;
  };
  gemini: {
    apiKey: string;
    alternativeApiKeys?: string[];
    rateLimits: {
      requestsPerMinute: number;
      tokensPerMinute: number;
      maxRetries: number;
      retryDelayMs: number;
    };
  };
}

// Get API keys from environment or fallback
const getApiKeysArray = (envVar: string, fallbackKey: string): string[] => {
  // Check for environment variable first
  const envKeys = import.meta.env[envVar];
  if (envKeys) {
    // If multiple keys are provided as comma-separated, split them
    if (typeof envKeys === 'string' && envKeys.includes(',')) {
      return envKeys.split(',').map(key => key.trim()).filter(key => key);
    }
    // Single key from env vars
    return [typeof envKeys === 'string' ? envKeys.trim() : envKeys];
  }
  // Fallback to hardcoded key
  return fallbackKey ? [fallbackKey] : [];
};

/**
 * API configuration object
 * 
 * Replace placeholder values with actual API keys before using.
 */
export const API_CONFIG: ApiConfig = {
  youtube: {
    apiKey: import.meta.env.VITE_YOUTUBE_API_KEY || "",
  },
  gemini: {
    // Primary API key (first one in the list or directly specified)
    apiKey: import.meta.env.VITE_GEMINI_API_KEY || "",
    
    // Alternative API keys to use when primary key hits rate limits
    alternativeApiKeys: getApiKeysArray('VITE_GEMINI_ALT_API_KEYS', '').filter(key => 
      key && key !== import.meta.env.VITE_GEMINI_API_KEY
    ),
    
    // Rate limit configuration
    rateLimits: {
      requestsPerMinute: 5,        // Max requests per minute (free tier is around 60 per minute)
      tokensPerMinute: 60000,      // Max tokens per minute (approximate for free tier)
      maxRetries: 3,               // Max retry attempts for rate limited requests
      retryDelayMs: 1000,          // Base delay between retries (will increase with backoff)
    }
  }
};

/**
 * YouTube category IDs for different content types
 * 
 * These are standard YouTube category IDs. For reference:
 * 1: Film & Animation
 * 2: Autos & Vehicles
 * 10: Music
 * 15: Pets & Animals
 * 17: Sports
 * 19: Travel & Events
 * 20: Gaming
 * 22: People & Blogs
 * 23: Comedy
 * 24: Entertainment
 * 25: News & Politics
 * 26: Howto & Style
 * 27: Education
 * 28: Science & Technology
 */
export const YOUTUBE_CATEGORY_IDS = {
  FILM_ANIMATION: "1",
  AUTOS_VEHICLES: "2",
  MUSIC: "10",
  PETS_ANIMALS: "15",
  SPORTS: "17",
  TRAVEL_EVENTS: "19",
  GAMING: "20",
  PEOPLE_BLOGS: "22",
  COMEDY: "23",
  ENTERTAINMENT: "24",
  NEWS_POLITICS: "25",
  HOWTO_STYLE: "26",
  EDUCATION: "27",
  SCIENCE_TECHNOLOGY: "28",
};

/**
 * Maps niche categories to YouTube category IDs
 */
export const NICHE_TO_CATEGORY_MAP: Record<string, string> = {
  "Gaming": YOUTUBE_CATEGORY_IDS.GAMING,
  "Beauty & Fashion": YOUTUBE_CATEGORY_IDS.HOWTO_STYLE,
  "Technology & Gadgets": YOUTUBE_CATEGORY_IDS.SCIENCE_TECHNOLOGY,
  "Travel & Lifestyle": YOUTUBE_CATEGORY_IDS.TRAVEL_EVENTS,
  "Food & Cooking": YOUTUBE_CATEGORY_IDS.HOWTO_STYLE,
  "Fitness & Health": YOUTUBE_CATEGORY_IDS.HOWTO_STYLE,
  "Education & Learning": YOUTUBE_CATEGORY_IDS.EDUCATION,
  "Entertainment": YOUTUBE_CATEGORY_IDS.ENTERTAINMENT,
  "Music": YOUTUBE_CATEGORY_IDS.MUSIC,
  "DIY & Crafts": YOUTUBE_CATEGORY_IDS.HOWTO_STYLE,
  "Business & Finance": YOUTUBE_CATEGORY_IDS.EDUCATION,
  "Sports": YOUTUBE_CATEGORY_IDS.SPORTS,
  "News & Politics": YOUTUBE_CATEGORY_IDS.NEWS_POLITICS,
  "Comedy": YOUTUBE_CATEGORY_IDS.COMEDY,
  "Automotive": YOUTUBE_CATEGORY_IDS.AUTOS_VEHICLES,
  "Pets & Animals": YOUTUBE_CATEGORY_IDS.PETS_ANIMALS,
  "Science & Technology": YOUTUBE_CATEGORY_IDS.SCIENCE_TECHNOLOGY,
  "Art & Design": YOUTUBE_CATEGORY_IDS.FILM_ANIMATION,
  "Parenting & Family": YOUTUBE_CATEGORY_IDS.PEOPLE_BLOGS,
  "Other": YOUTUBE_CATEGORY_IDS.PEOPLE_BLOGS,
};

/**
 * Default settings for API requests
 */
export const API_DEFAULTS = {
  youtube: {
    maxResults: 5,  // Number of results to fetch from YouTube API
  },
  gemini: {
    titlesToGenerate: 3, // Number of titles to generate using Gemini
  }
};