/**
 * Google Gemini API client facade
 * 
 * This file provides functions for interacting with Google's Gemini AI model
 * by re-exporting functionalities from specialized service modules.
 */

// Re-export title generation functionalities
export { 
    generateOptimizedTitles,
    type GeminiTitleGenerationRequest,
    type GeminiTitleGenerationResponse 
} from './gemini/titleService';

// Re-export description generation functionalities
export {
    generateDescription,
    type DescriptionGenerationRequest,
    type DescriptionGenerationResponse
} from './gemini/descriptionService';

// Re-export tag generation functionalities
export {
    generateTags,
    type TagGenerationRequest,
    type TagGenerationResponse
} from './gemini/tagService';