import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { NICHE_TO_CATEGORY_MAP } from "@/lib/config";
import { getTopVideosByCategory } from "@/lib/youtubeApi";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface TrendingTitlesButtonProps {
  niche?: string;
  onTitlesFound?: (videos: any[]) => void;
  disabled?: boolean;
}

const TrendingTitlesButton: React.FC<TrendingTitlesButtonProps> = ({
  niche,
  onTitlesFound,
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  
  const fetchTrendingTitles = async () => {
    if (!niche) {
      toast({
        variant: "destructive",
        title: "No niche selected",
        description: "Please select a content niche to find trending titles."
      });
      return;
    }

    setIsLoading(true);
    try {
      // Get the YouTube category ID for the selected niche
      const categoryId = NICHE_TO_CATEGORY_MAP[niche] || NICHE_TO_CATEGORY_MAP["Other"];
      
      // Fetch top videos from YouTube API
      const topVideos = await getTopVideosByCategory(categoryId);
      
      if (!topVideos) {
        throw new Error("Failed to fetch trending videos");
      }
      
      // Combine trending and most viewed videos
      const combinedVideos = [
        ...topVideos.trending,
        ...topVideos.mostViewed
      ];
      
      // Remove duplicates (based on title)
      const uniqueVideos = combinedVideos.filter((video, index, self) =>
        index === self.findIndex((v) => v.title === video.title)
      );
      
      // Call the callback with the found videos if provided
      if (onTitlesFound) {
        onTitlesFound(uniqueVideos);
      }
      
      toast({
        title: "Trending Titles Found",
        description: `Found ${uniqueVideos.length} trending videos in your category.`
      });
      
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Failed to fetch trending titles",
        description: error.message || "Please try again later."
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="outline" 
            size="sm"
            className="gap-2 border-brand-purple/20 hover:bg-brand-purple/5 hover:text-brand-purple"
            onClick={fetchTrendingTitles}
            disabled={isLoading || disabled}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 text-brand-purple" />
                Refresh Trending Titles
              </>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Update trending videos from YouTube</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default TrendingTitlesButton;