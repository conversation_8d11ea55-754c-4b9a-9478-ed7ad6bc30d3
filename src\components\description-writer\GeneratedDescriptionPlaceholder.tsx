import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card";
import { FileText } from "lucide-react";

interface GeneratedDescriptionPlaceholderProps {
  isLoading: boolean;
}

const GeneratedDescriptionPlaceholder: React.FC<GeneratedDescriptionPlaceholderProps> = ({ isLoading }) => {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <span className="h-6 w-6 bg-brand-purple/10 rounded-full flex items-center justify-center">
            <FileText className="h-3 w-3 text-brand-purple" />
          </span>
          Generated Description
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-4">
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse h-4 bg-muted rounded-lg"></div>
            <div className="animate-pulse h-4 bg-muted rounded-lg w-[90%]"></div>
            <div className="animate-pulse h-4 bg-muted rounded-lg w-[95%]"></div>
            <div className="animate-pulse h-4 bg-muted rounded-lg w-[80%]"></div>
            <div className="animate-pulse h-4 bg-muted rounded-lg"></div>
            <div className="animate-pulse h-4 bg-muted rounded-lg w-[85%]"></div>
            <div className="animate-pulse h-4 bg-muted rounded-lg w-[90%]"></div>
            <div className="animate-pulse h-4 bg-muted rounded-lg w-[60%]"></div>
          </div>
        ) : (
          <div className="h-[400px] flex flex-col items-center justify-center text-center">
            <div className="h-16 w-16 bg-brand-purple/10 rounded-full flex items-center justify-center mb-4">
              <FileText className="h-8 w-8 text-brand-purple" />
            </div>
            <h3 className="font-semibold text-lg mb-2">AI Description Generator</h3>
            <p className="text-muted-foreground max-w-sm">
              Fill out the form with your video details to generate an SEO-optimized description with affiliate links and timestamps.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GeneratedDescriptionPlaceholder;
