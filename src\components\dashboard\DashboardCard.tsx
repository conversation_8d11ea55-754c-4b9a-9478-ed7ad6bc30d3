import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, Video, FileText, Image, Tag, BarChart, Search, Loader2 } from "lucide-react";
import { useVideo } from "@/contexts/VideoContext";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

type DashboardCardProps = {
  title: string;
  description: string;
  icon: string;
  linkText: string;
  linkUrl: string;
  actionType?: 'title' | 'thumbnail' | 'description' | 'tag';
  disabled?: boolean;
};

const DashboardCard = ({
  title,
  description,
  icon,
  linkText,
  linkUrl,
  actionType,
  disabled = false
}: DashboardCardProps) => {
  const { videoData, setIsProcessing } = useVideo();
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const renderIcon = () => {
    switch (icon) {
      case "video":
        return <Video className="h-5 w-5" />;
      case "file-text":
        return <FileText className="h-5 w-5" />;
      case "image":
        return <Image className="h-5 w-5" />;
      case "tag":
        return <Tag className="h-5 w-5" />;
      case "bar-chart":
        return <BarChart className="h-5 w-5" />;
      case "search":
        return <Search className="h-5 w-5" />;
      default:
        return <Video className="h-5 w-5" />;
    }
  };

  const handleAction = async () => {
    // If disabled or no video data, don't do anything
    if (disabled) return;

    // If no video is uploaded and this is a feature that requires video, show a message
    if (!videoData && actionType) {
      toast({
        title: "No Video Selected",
        description: "Please upload a video or provide a YouTube URL first.",
        variant: "destructive"
      });
      return;
    }

    // If this is a feature that requires video processing
    if (actionType && videoData) {
      setIsLoading(true);
      setIsProcessing(true);

      try {
        // In a real implementation, you would process the video here
        // For now, we'll just simulate a delay and then navigate
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Pass all video data including metadata to the feature page
        navigate(linkUrl, {
          state: {
            videoData: {
              ...videoData,
              // Add any additional processing based on action type if needed
              actionType: actionType
            }
          }
        });
      } catch (error) {
        toast({
          title: "Processing Failed",
          description: "There was an error processing your video. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
        setIsProcessing(false);
      }
      return;
    }

    // For features that don't require video processing, just navigate
    navigate(linkUrl);
  };

  return (
    <Card className={cn(
      "border hover:shadow-md transition-all overflow-hidden",
      disabled ? "opacity-70" : "hover:border-brand-purple/30",
      !disabled && "hover:translate-y-[-2px]"
    )}>
      <CardHeader className={cn(
        "pb-2",
        disabled ? "bg-gray-100 dark:bg-gray-800" : "bg-gradient-to-r from-brand-purple/5 to-brand-blue/5"
      )}>
        <div className="flex items-center gap-3">
          <div className={cn(
            "p-2 rounded-md",
            disabled 
              ? "bg-gray-200 dark:bg-gray-700 text-gray-500" 
              : "bg-gradient-to-r from-brand-purple/20 to-brand-blue/20 text-brand-purple"
          )}>
            {renderIcon()}
          </div>
          <CardTitle className={cn(
            "text-lg",
            disabled ? "text-gray-500" : ""
          )}>{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="pt-4">
        <p className={cn(
          "text-muted-foreground text-sm",
          disabled ? "text-gray-400" : ""
        )}>{description}</p>
      </CardContent>
      <CardFooter>
        {disabled ? (
          <Button 
            variant="outline" 
            disabled 
            className="w-full justify-between bg-gray-50 dark:bg-gray-800 text-gray-400"
          >
            {linkText} <ArrowRight className="ml-2 h-4 w-4" />
            <span className="ml-auto text-xs bg-gray-200 dark:bg-gray-700 rounded-full px-2 py-0.5">Coming soon</span>
          </Button>
        ) : (
          <Button
            variant="outline"
            className={cn(
              "w-full justify-between border-brand-purple/30 hover:bg-brand-purple/5",
              actionType === "title" && "border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20",
              actionType === "thumbnail" && "border-green-300 hover:bg-green-50 dark:hover:bg-green-900/20",
              actionType === "description" && "border-amber-300 hover:bg-amber-50 dark:hover:bg-amber-900/20",
              actionType === "tag" && "border-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20"
            )}
            onClick={handleAction}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                {linkText} <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default DashboardCard;
