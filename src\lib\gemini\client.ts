import { API_CONFIG } from '../config'; // Adjusted path to be relative to src/lib/gemini/
import { toast } from '../../components/ui/use-toast'; // Adjusted path

const GEMINI_API_BASE_URL = "https://generativelanguage.googleapis.com/v1/models/";

// Use rate limit values from config
const RATE_LIMIT = {
  requestsPerMinute: API_CONFIG.gemini.rateLimits?.requestsPerMinute || 5,
  tokensPerMinute: API_CONFIG.gemini.rateLimits?.tokensPerMinute || 60000,
  retryDelay: API_CONFIG.gemini.rateLimits?.retryDelayMs || 1000,
  maxRetries: API_CONFIG.gemini.rateLimits?.maxRetries || 3,
  requestQueue: [] as (() => Promise<any>)[],
  lastRequestTime: 0,
  requestsThisMinute: 0,
  tokensThisMinute: 0,
  processingQueue: false,
  resetTime: Date.now(),
  apiKeyIndex: 0, // Track which API key we're currently using
};

// Get all available API keys
const getAllApiKeys = (): string[] => {
  const keys: string[] = [];
  
  // Add primary key if valid
  if (API_CONFIG.gemini.apiKey) {
    // Clean the API key - remove spaces and make sure there's no comma
    const cleanedKey = API_CONFIG.gemini.apiKey.trim();
    if (cleanedKey && !cleanedKey.includes(',')) {
      keys.push(cleanedKey);
    } else if (cleanedKey.includes(',')) {
      // If the key contains commas, split it and add each part as a separate key
      console.warn("Primary API key contains commas. Splitting into multiple keys. Please update your .env.local file to use VITE_GEMINI_ALT_API_KEYS for multiple keys.");
      const splitKeys = cleanedKey.split(',').map(k => k.trim()).filter(k => k !== "");
      if (splitKeys.length > 0) {
        keys.push(splitKeys[0]); // Add first one as primary
        // Add the rest as alternatives later
      }
    }
  }
  
  // Add alternative keys if available
  if (API_CONFIG.gemini.alternativeApiKeys?.length) {
    const alternativeKeys = API_CONFIG.gemini.alternativeApiKeys
      .flatMap(key => {
        // If a key in alternativeApiKeys contains commas, split it further
        if (key.includes(',')) {
          return key.split(',').map(k => k.trim()).filter(k => k !== "");
        }
        return key.trim();
      })
      .filter(k => k && !keys.includes(k)); // Remove duplicates and empty keys
    
    keys.push(...alternativeKeys);
  }
  
  if (keys.length === 0) {
    console.error("No valid Gemini API keys found. Please check your .env.local file.");
  } else {
    console.log(`Found ${keys.length} Gemini API key(s) to use.`);
  }
  
  return keys;
};

// Reset rate limit counters every minute
setInterval(() => {
  RATE_LIMIT.requestsThisMinute = 0;
  RATE_LIMIT.tokensThisMinute = 0;
  RATE_LIMIT.resetTime = Date.now();
}, 60000);

interface GeminiApiCallOptions {
  temperature?: number;
  topK?: number;
  topP?: number;
  maxOutputTokens?: number;
}

// Function to estimate token count (very rough estimate)
function estimateTokenCount(text: string): number {
  // Gemini's tokens are roughly 4 characters per token on average
  return Math.ceil(text.length / 4);
}

// Process the request queue
async function processRequestQueue() {
  if (RATE_LIMIT.processingQueue || RATE_LIMIT.requestQueue.length === 0) return;
  
  RATE_LIMIT.processingQueue = true;
  
  try {
    // Reset counters if a minute has passed
    if (Date.now() - RATE_LIMIT.resetTime >= 60000) {
      RATE_LIMIT.requestsThisMinute = 0;
      RATE_LIMIT.tokensThisMinute = 0;
      RATE_LIMIT.resetTime = Date.now();
    }
    
    // If we're under the limits, process the next request
    if (RATE_LIMIT.requestsThisMinute < RATE_LIMIT.requestsPerMinute) {
      const nextRequest = RATE_LIMIT.requestQueue.shift();
      if (nextRequest) {
        RATE_LIMIT.requestsThisMinute++;
        RATE_LIMIT.lastRequestTime = Date.now();
        await nextRequest();
      }
    }
  } finally {
    RATE_LIMIT.processingQueue = false;
    
    // If there are more requests and we're not at the limit, process another
    if (RATE_LIMIT.requestQueue.length > 0 && 
        RATE_LIMIT.requestsThisMinute < RATE_LIMIT.requestsPerMinute) {
      setTimeout(processRequestQueue, 100);
    } else if (RATE_LIMIT.requestQueue.length > 0) {
      // If we've hit the limit, wait until we can process more
      const timeToNextReset = 60000 - (Date.now() - RATE_LIMIT.resetTime);
      setTimeout(processRequestQueue, timeToNextReset + 100);
    }
  }
}

export async function callGeminiApi(
  prompt: string,
  apiKey: string,
  model: string, // The model name (e.g., "gemini-pro")
  options?: GeminiApiCallOptions
): Promise<string> { // Returns the generated text directly or throws
  // Extract just the model name from any format that might be passed in
  const modelName = model.includes(":") ? model.split(":")[0] : model;
  // Use gemini-1.5-flash as the default model
  const actualModel = modelName === "gemini-pro" ? "gemini-1.5-flash" : modelName;
  console.log(`Requested model: ${model}, using model: ${actualModel}`);
  
  // Correctly format the API endpoint
  const apiEndpoint = `${GEMINI_API_BASE_URL}${actualModel}:generateContent`;
  
  const defaultOptions: GeminiApiCallOptions = {
    temperature: 0.6, // Slightly lower temperature for more consistent results
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 512, // Reduced token count
    ...options,
  };

  // Get all API keys we can use
  const allApiKeys = getAllApiKeys();
    // Get all keys ready but start with the primary key first
  const allAvailableKeys = allApiKeys;
  let keysToTry = [apiKey];

  // Estimate token count
  const estimatedTokens = estimateTokenCount(prompt);
    // Return a Promise that will be resolved when the request can be processed
  return new Promise((resolve, reject) => {
    const executeRequest = async () => {
      let primaryKeyFailed = false;
      
      for (const key of keysToTry) {
        // If the primary key failed and we haven't added alternative keys yet, add them now
        if (primaryKeyFailed && keysToTry.length === 1) {
          console.log("Primary API key failed, trying alternative keys...");
          // Add alternative keys now that primary key has failed
          const alternativeKeys = allAvailableKeys.filter(k => k !== apiKey);
          if (alternativeKeys.length > 0) {
            console.log(`Found ${alternativeKeys.length} alternative API keys to try.`);
            keysToTry = keysToTry.concat(alternativeKeys);
          } else {
            console.warn("No alternative API keys available after primary key failed");
          }
        }
        
        let retries = 0;
        
        while (retries <= RATE_LIMIT.maxRetries) {
          try {
            console.log(`Attempting request with ${actualModel} (retry ${retries}/${RATE_LIMIT.maxRetries})`);
            
            const response = await fetch(`${apiEndpoint}?key=${key}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                contents: [{ parts: [{ text: prompt }] }],
                generationConfig: defaultOptions,
              }),
            });            if (response.status === 429) {
              // Rate limit exceeded
              const retryAfter = response.headers.get('retry-after');
              let delayMs = RATE_LIMIT.retryDelay * Math.pow(2, retries); // Exponential backoff
              
              if (retryAfter) {
                delayMs = parseInt(retryAfter) * 1000;
              }
              
              // If this is the primary key and we've hit the rate limit, 
              // mark it as failed so we can try alternative keys
              if (key === apiKey) {
                primaryKeyFailed = true;
              }
              
              retries++;
              
              if (retries <= RATE_LIMIT.maxRetries) {
                console.warn(`Rate limit exceeded. Retrying in ${delayMs}ms (${retries}/${RATE_LIMIT.maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, delayMs));
                continue; // Try again after delay
              } else {
                console.warn(`Rate limit exceeded with API key. Trying next key if available.`);
                break; // Try next API key
              }
            }
              if (!response.ok) {
              const errorText = await response.text();
              console.error(`Gemini API error (${actualModel}): ${response.statusText} (${response.status})`, errorText);
              
              // If this is the primary key and it failed, mark it for trying alternative keys
              if (key === apiKey) {
                primaryKeyFailed = true;
                console.log("Primary API key failed with status:", response.status);
              }
              
              // For 404 errors, the model format might be incorrect
              if (response.status === 404) {
                console.error("404 Not Found error. This could indicate an incorrect model name or endpoint format.");
                // If this is the last key, throw a more helpful error message
                if (key === keysToTry[keysToTry.length - 1]) {
                  throw new Error(`Model not found: ${actualModel}. Please check API documentation for supported models.`);
                }
              }
              
              // If not a rate limit error, or if this is the last key, throw error
              if (response.status !== 429 || key === keysToTry[keysToTry.length - 1]) {
                throw new Error(`Gemini API error: ${response.statusText} (${response.status})`);
              }
              
              break; // Try next key
            }

            const result = await response.json();

            if (!result.candidates || !result.candidates[0]?.content?.parts?.[0]?.text) {
              console.error("Invalid response format from Gemini API:", result);
              throw new Error("Invalid response format from Gemini API");
            }

            resolve(result.candidates[0].content.parts[0].text);
            return;
          } catch (error) {
            if (error instanceof Error) {
              if (error.message.includes('429')) {
                // If rate limited and we have retries left, try again
                if (retries < RATE_LIMIT.maxRetries) {
                  retries++;
                  const delayMs = RATE_LIMIT.retryDelay * Math.pow(2, retries);
                  await new Promise(resolve => setTimeout(resolve, delayMs));
                  continue;
                }
                
                // If we're out of retries, try the next key
                break;
              } else {
                // If this is the last key and we're still failing, reject
                if (key === keysToTry[keysToTry.length - 1]) {
                  reject(error);
                  return;
                }
                
                // Otherwise try the next key
                break;
              }
            } else {
              // If not an Error object, reject immediately
              reject(error);
              return;
            }
          }
        }
      }
      
      // If we got here, we've tried all API keys and still failed
      reject(new Error("Failed to call Gemini API with all available API keys"));
    };
    
    // Add this request to the queue
    RATE_LIMIT.requestQueue.push(executeRequest);
    
    // Start processing the queue if it's not already being processed
    if (!RATE_LIMIT.processingQueue) {
      processRequestQueue();
    }
  });
}
