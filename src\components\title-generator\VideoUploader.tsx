import React, { useState } from "react";
import { toast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, Link as LinkIcon, ArrowRight } from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

type VideoUploaderProps = {
  onVideoSubmit: (file: File | string, metadata?: { niche?: string }) => void;
  isLoading: boolean;
};

const VideoUploader = ({ onVideoSubmit, isLoading }: VideoUploaderProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [activeTab, setActiveTab] = useState("upload");
  const [niche, setNiche] = useState<string>("");
  
  // YouTube niches - same as in HomeVideoForm for consistency
  const youtubeNiches = [
    "Gaming",
    "Beauty & Fashion",
    "Technology & Gadgets",
    "Travel & Lifestyle",
    "Food & Cooking",
    "Fitness & Health",
    "Education & Learning",
    "Entertainment",
    "Music",
    "DIY & Crafts",
    "Business & Finance",
    "Sports",
    "News & Politics",
    "Comedy",
    "Automotive",
    "Pets & Animals",
    "Science & Technology",
    "Art & Design",
    "Parenting & Family",
    "Other"
  ];

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      
      // Check if file is a video
      if (!selectedFile.type.startsWith("video/")) {
        toast({
          variant: "destructive",
          title: "Invalid file type",
          description: "Please upload a video file.",
        });
        return;
      }
      
      // Check if file size is under 100MB
      if (selectedFile.size > 100 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "File too large",
          description: "Please upload a video under 100MB.",
        });
        return;
      }
      
      setFile(selectedFile);
    }
  };

  const handleUploadSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (file) {
      onVideoSubmit(file, { niche });
    } else {
      toast({
        variant: "destructive",
        title: "No file selected",
        description: "Please select a video file to upload.",
      });
    }
  };

  const handleYoutubeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!youtubeUrl) {
      toast({
        variant: "destructive",
        title: "No URL provided",
        description: "Please enter a YouTube video URL.",
      });
      return;
    }
    
    // Simple YouTube URL validation
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/;
    if (!youtubeRegex.test(youtubeUrl)) {
      toast({
        variant: "destructive",
        title: "Invalid URL",
        description: "Please enter a valid YouTube video URL.",
      });
      return;
    }
    
    onVideoSubmit(youtubeUrl, { niche });
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="upload">Upload Video</TabsTrigger>
            <TabsTrigger value="youtube">YouTube URL</TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload">
            <form onSubmit={handleUploadSubmit} className="space-y-6">
              <div className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => document.getElementById("video-upload")?.click()}>
                <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-medium mb-1">Upload your video</h3>
                <p className="text-sm text-muted-foreground mb-4">Drag and drop or click to browse</p>
                <p className="text-xs text-muted-foreground">MP4, MOV, or WebM • Up to 100MB</p>
                
                <Input 
                  id="video-upload" 
                  type="file" 
                  accept="video/*" 
                  className="hidden"
                  onChange={handleFileChange} 
                />
              </div>
              
              {file && (
                <div className="p-4 bg-muted rounded-lg flex justify-between items-center">
                  <div className="truncate">
                    <p className="font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {(file.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                  </div>
                  <Button 
                    type="button"
                    variant="ghost" 
                    size="sm"
                    onClick={() => setFile(null)}
                  >
                    Remove
                  </Button>
                </div>
              )}
              
              <div>
                <label className="block text-sm font-medium mb-2">
                  Select YouTube Niche
                </label>
                <Select onValueChange={setNiche} defaultValue="" className="w-full">
                  <SelectTrigger className="border-2 rounded-lg p-3 text-left">
                    <SelectValue placeholder="Select a niche" />
                  </SelectTrigger>
                  <SelectContent>
                    {youtubeNiches.map((niche) => (
                      <SelectItem key={niche} value={niche}>
                        {niche}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                type="submit" 
                className="w-full"
                disabled={!file || isLoading}
              >
                {isLoading ? "Analyzing Video..." : "Generate Titles"}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </form>
          </TabsContent>
          
          <TabsContent value="youtube">
            <form onSubmit={handleYoutubeSubmit} className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <LinkIcon className="h-4 w-4 text-muted-foreground" />
                  <label htmlFor="youtube-url" className="font-medium text-sm">
                    YouTube Video URL
                  </label>
                </div>
                <Input 
                  id="youtube-url" 
                  placeholder="https://www.youtube.com/watch?v=..." 
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Enter a public YouTube video URL
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">
                  Select YouTube Niche
                </label>
                <Select onValueChange={setNiche} defaultValue="" className="w-full">
                  <SelectTrigger className="border-2 rounded-lg p-3 text-left">
                    <SelectValue placeholder="Select a niche" />
                  </SelectTrigger>
                  <SelectContent>
                    {youtubeNiches.map((niche) => (
                      <SelectItem key={niche} value={niche}>
                        {niche}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                type="submit" 
                className="w-full"
                disabled={!youtubeUrl || isLoading}
              >
                {isLoading ? "Analyzing Video..." : "Generate Titles"}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default VideoUploader;
