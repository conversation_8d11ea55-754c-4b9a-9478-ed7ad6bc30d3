# YouTube Analytics Integration Guide

This guide will help you set up real YouTube analytics in your application. Currently, the app shows mock data, but following these steps will enable real YouTube analytics data fetching and display.

## Current Implementation Status

✅ **Completed:**
- Database schema for storing YouTube analytics data
- Supabase integration for data storage
- YouTube Analytics API service functions
- UI components for displaying analytics
- Real-time data fetching with fallback to mock data

⚠️ **Needs Implementation:**
- YouTube OAuth2 authentication flow
- Token management and refresh
- Scheduled data synchronization
- Error handling for API rate limits

## Prerequisites

1. **Google Cloud Project** with billing enabled
2. **YouTube Data API v3** and **YouTube Analytics API** enabled
3. **OAuth 2.0 credentials** configured
4. **Supabase database** with analytics tables

## Step-by-Step Setup

### 1. Google Cloud Console Setup

1. **Create/Select Project:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one
   - Enable billing (required for YouTube Analytics API)

2. **Enable APIs:**
   - Navigate to APIs & Services > Library
   - Enable **YouTube Data API v3**
   - Enable **YouTube Analytics API**

3. **Create OAuth Credentials:**
   - Go to APIs & Services > Credentials
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Select "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:5173/auth/callback` (development)
     - `https://your-domain.com/auth/callback` (production)

4. **Configure OAuth Consent Screen:**
   - Add your app name, support email, and developer contact
   - Add the following scopes:
     - `https://www.googleapis.com/auth/youtube.readonly`
     - `https://www.googleapis.com/auth/yt-analytics.readonly`

### 2. Environment Variables

Add these to your `.env` file:

```env
# YouTube/Google OAuth
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_GOOGLE_CLIENT_SECRET=your_google_client_secret
VITE_GOOGLE_REDIRECT_URI=http://localhost:5173/auth/callback

# YouTube Data API (existing)
VITE_YOUTUBE_API_KEY=your_youtube_api_key

# Supabase (existing)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Database Migration

Run the database migration to create the YouTube analytics tables:

```sql
-- This is already created in supabase/migrations/001_youtube_analytics.sql
-- Apply it to your Supabase database through the dashboard or CLI
```

### 4. OAuth Implementation

You'll need to implement the OAuth flow. Here's what needs to be added:

#### A. OAuth Service (`src/lib/googleAuth.ts`)

```typescript
// This file needs to be created with Google OAuth implementation
export const initiateGoogleAuth = () => {
  // Redirect to Google OAuth consent screen
};

export const handleAuthCallback = (code: string) => {
  // Exchange code for access and refresh tokens
  // Store tokens securely in Supabase
};
```

#### B. Auth Callback Route

Add a route handler for `/auth/callback` to process the OAuth response.

#### C. Token Management

Implement token refresh logic to handle expired access tokens.

### 5. Data Synchronization

#### A. Manual Sync

Users can click "Refresh Data" to manually fetch latest analytics.

#### B. Scheduled Sync (Optional)

Set up a cron job or serverless function to periodically fetch analytics data:

- Daily sync for channel analytics
- Weekly sync for video analytics
- Respect API rate limits (10,000 requests/day for YouTube Analytics API)

### 6. Testing the Integration

1. **Connect Channel:**
   - Click "Connect YouTube Channel" in the dashboard
   - Complete OAuth flow
   - Verify channel appears in database

2. **View Analytics:**
   - Check that real data replaces mock data
   - Verify data updates when refreshed
   - Test error handling for API failures

## Implementation Files Overview

### New Files Created:

1. **`src/lib/youtubeAnalytics.ts`** - YouTube Analytics API integration
2. **`src/lib/supabaseAnalytics.ts`** - Database operations for analytics
3. **`src/components/dashboard/analytics/RealAnalyticsSummary.tsx`** - Real analytics display
4. **`src/components/dashboard/analytics/YouTubeOAuthSetup.tsx`** - OAuth setup UI
5. **`supabase/migrations/001_youtube_analytics.sql`** - Database schema

### Modified Files:

1. **`src/integrations/supabase/types.ts`** - Added new table types
2. **`src/components/dashboard/overview/DashboardOverview.tsx`** - Uses real analytics

## Features Implemented

### Analytics Display
- ✅ Real-time YouTube analytics (views, watch time, subscribers, likes)
- ✅ Percentage change calculations (compared to previous period)
- ✅ Fallback to mock data when no channel connected
- ✅ Loading states and error handling

### Data Management
- ✅ Secure token storage in Supabase
- ✅ Row-level security policies
- ✅ Aggregated analytics calculations
- ✅ Historical data tracking

### User Interface
- ✅ Channel connection status indicator
- ✅ OAuth setup instructions
- ✅ Manual data refresh
- ✅ Responsive design

## Next Steps

To complete the integration:

1. **Implement OAuth Flow** - Add Google OAuth authentication
2. **Add Token Refresh** - Handle expired tokens automatically
3. **Set Up Sync Jobs** - Schedule regular data fetching
4. **Add More Metrics** - Expand analytics to include more YouTube metrics
5. **Error Handling** - Improve error handling for API failures and rate limits

## API Rate Limits

Be aware of YouTube API quotas:
- **YouTube Data API v3:** 10,000 units/day (free tier)
- **YouTube Analytics API:** 10,000 requests/day (free tier)

Plan your data fetching strategy accordingly to stay within limits.

## Security Considerations

- Store OAuth tokens encrypted in Supabase
- Use HTTPS for all OAuth redirects
- Implement proper session management
- Regular token rotation
- Respect user privacy and data access permissions

This implementation provides a solid foundation for YouTube analytics integration. The hardest part (OAuth flow) still needs to be implemented, but all the supporting infrastructure is now in place.
