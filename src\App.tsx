// filepath: c:\Users\<USER>\Desktop\video-growth-ai-lab\src\App.tsx
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import AIFeatures from "./pages/AIFeatures";
import TitleGenerator from "./pages/TitleGenerator";
import ThumbnailCreator from "./pages/ThumbnailCreator";
import DescriptionWriter from "./pages/DescriptionWriter";
import TagOptimizer from "./pages/TagOptimizer";
import NewVideo from "./pages/NewVideo";
import Auth from "./pages/Auth";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <BrowserRouter>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/auth" element={<Auth />} />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/ai-features" element={
              <ProtectedRoute>
                <AIFeatures />
              </ProtectedRoute>
            } />
            <Route path="/title-generator" element={
              <ProtectedRoute>
                <TitleGenerator />
              </ProtectedRoute>
            } />
            <Route path="/thumbnail-creator" element={
              <ProtectedRoute>
                <ThumbnailCreator />
              </ProtectedRoute>
            } />            <Route path="/description-writer" element={
              <ProtectedRoute>
                <DescriptionWriter />
              </ProtectedRoute>
            } />
            <Route path="/tag-optimizer" element={
              <ProtectedRoute>
                <TagOptimizer />
              </ProtectedRoute>
            } />
            <Route path="/new-video" element={
              <ProtectedRoute>
                <NewVideo />
              </ProtectedRoute>
            } />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </TooltipProvider>
      </AuthProvider>
    </BrowserRouter>
  </QueryClientProvider>
);

export default App;
