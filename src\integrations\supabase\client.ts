// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wqgvjyjjhipiqihatwtr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxZ3ZqeWpqaGlwaXFpaGF0d3RyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyNzc0ODgsImV4cCI6MjA2MTg1MzQ4OH0.4A6qgOsxmkuTtRmEwIIi7Qq5wYL-mQhF8mH_oMC0rKo";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);