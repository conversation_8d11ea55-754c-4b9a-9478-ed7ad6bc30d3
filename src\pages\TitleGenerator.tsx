import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Sidebar from "@/components/dashboard/Sidebar";
import Header from "@/components/dashboard/Header";
import { toast } from "@/components/ui/use-toast";
import { generateOptimizedTitles } from "@/lib/geminiApi";
import { getTopVideosByCategory } from "@/lib/youtubeApi";
import { NICHE_TO_CATEGORY_MAP } from "@/lib/config";
import TitleGeneratorContainer from "@/components/title-generator/TitleGeneratorContainer";
import { useVideo, VideoProvider } from "@/contexts/VideoContext";

// Inner component to use the useVideo hook
const TitleGeneratorContent = () => {
  const { videoData } = useVideo();
  const location = useLocation();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [generatedTitles, setGeneratedTitles] = useState<string[]>([]);
  const [videoNiche, setVideoNiche] = useState<string>("Entertainment");
  const [trendingVideos, setTrendingVideos] = useState<any[]>([]);
  const [videoDetails, setVideoDetails] = useState({
    title: "",
    description: ""
  });
  const [error, setError] = useState<string | null>(null);

  // Check if video data was passed from the dashboard or context
  useEffect(() => {
    // First check context, then location state
    const videoDataSource = videoData || location.state?.videoData;
    
    if (videoDataSource) {
      if (videoDataSource.niche) {
        setVideoNiche(videoDataSource.niche);
      }
      
      setVideoDetails({
        title: videoDataSource.potentialTitle || "",
        description: videoDataSource.description || ""
      });
      
      loadTrendingVideos(videoDataSource.niche || "Entertainment");
    } else {
      // If no data is available, redirect to AI features page
      toast({
        variant: "destructive",
        title: "No video selected",
        description: "Please select a video first.",
      });
      navigate('/ai-features');
    }
  }, [location.state, videoData, navigate]);
  
  const loadTrendingVideos = async (niche: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Get the YouTube category ID for the selected niche
      const categoryId = NICHE_TO_CATEGORY_MAP[niche] || NICHE_TO_CATEGORY_MAP["Other"];
      
      // Fetch top videos from YouTube API
      const topVideos = await getTopVideosByCategory(categoryId);
      
      if (!topVideos) {
        throw new Error("Failed to fetch trending videos");
      }
      
      // Combine trending and most viewed videos
      const combinedVideos = [
        ...topVideos.trending,
        ...topVideos.mostViewed
      ];
      
      // Remove duplicates (based on title)
      const uniqueVideos = combinedVideos.filter((video, index, self) =>
        index === self.findIndex((v) => v.title === video.title)
      );
      
      // Sort by view count if available
      const sortedVideos = uniqueVideos.sort((a, b) => {
        if (a.viewCount && b.viewCount) {
          return b.viewCount - a.viewCount;
        }
        return 0;
      });
      
      setTrendingVideos(sortedVideos);
      
      if (sortedVideos.length > 0) {
        toast({
          title: "Trending Videos Loaded",
          description: `Found ${uniqueVideos.length} trending videos in "${niche}" category.`
        });
      } else {
        toast({
          variant: "destructive",
          title: "No Trending Videos Found",
          description: `No trending videos found for the "${niche}" category. Try another category.`
        });
      }
    } catch (error: any) {
      console.error("Error loading trending videos:", error);
      setError(`Failed to load trending videos: ${error.message}`);
      toast({
        variant: "destructive",
        title: "Failed to fetch trending videos",
        description: error.message || "Please try again later."
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleNicheChange = (niche: string) => {
    setVideoNiche(niche);
    setGeneratedTitles([]);
    loadTrendingVideos(niche);
  };
  
  const generateAITitles = async () => {
    setIsGeneratingAI(true);
    setError(null);
    
    try {
      // Extract just the titles for the API request
      const trendingTitles = trendingVideos.map(video => video.title);
      
      if (trendingTitles.length === 0) {
        throw new Error("No trending titles available. Please load trending videos first.");
      }
      
      console.log("Generating titles with the following data:", {
        trendingTitlesCount: trendingTitles.length,
        niche: videoNiche,
        hasUserTitle: !!videoDetails.title,
        hasDescription: !!videoDetails.description
      });
      
      // Call Gemini API
      const result = await generateOptimizedTitles({
        trendingTitles,
        originalTitle: videoDetails.title,
        description: videoDetails.description,
        niche: videoNiche
      });
      
      if (result && result.titles && result.titles.length > 0) {
        setGeneratedTitles(result.titles);
        toast({
          title: "AI Titles Generated!",
          description: `Generated ${result.titles.length} optimized title suggestions.`
        });
      } else {
        throw new Error("Failed to generate AI titles");
      }
    } catch (error: any) {
      console.error("Title generation error:", error);
      setError(`Title generation failed: ${error.message}`);
      toast({
        variant: "destructive",
        title: "AI Title Generation Failed",
        description: error.message || "Please try again later."
      });
    } finally {
      setIsGeneratingAI(false);
    }
  };
  
  const handleRefreshTrending = () => {
    loadTrendingVideos(videoNiche);
  };
    const handleResetTitles = () => {
    setGeneratedTitles([]);
  };
  
  const handleBack = () => {
    navigate('/ai-features', { state: { videoData: location.state?.videoData } });
  };
    return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-muted/30">
      <div className="w-64 hidden lg:block">
        <Sidebar />
      </div>
      <div className="lg:hidden">
        <Sidebar />
      </div>

      <div className="flex-1 flex flex-col lg:pt-0 pt-16">
        <Header
          title="AI Title Generator"
          subtitle="Create high-performing titles for your YouTube videos"
        />

        <main className="flex-1 p-4 md:p-6">
          <TitleGeneratorContainer
            videoNiche={videoNiche}
            videoDetails={videoDetails}
            error={error}
            isLoading={isLoading}
            isGeneratingAI={isGeneratingAI}
            trendingVideos={trendingVideos}
            generatedTitles={generatedTitles}
            onBack={handleBack}
            onNicheChange={handleNicheChange}
            onDetailsChange={setVideoDetails}
            onRefresh={handleRefreshTrending}
            onGenerate={generateAITitles}
            onResetTitles={handleResetTitles}
          />
        </main>
      </div>
    </div>
  );
};

const TitleGenerator = () => {
  return (
    <VideoProvider>
      <TitleGeneratorContent />
    </VideoProvider>
  );
};

export default TitleGenerator;
