import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Loader2, Tag } from "lucide-react";

interface GeneratedTagsPlaceholderProps {
  isLoading: boolean;
}

const GeneratedTagsPlaceholder: React.FC<GeneratedTagsPlaceholderProps> = ({ isLoading }) => {  return (
    <div className="min-h-[300px] flex flex-col items-center justify-center py-8">
      {isLoading ? (
        <div className="flex flex-col items-center text-center">
          <div className="w-16 h-16 rounded-full bg-brand-purple/10 flex items-center justify-center mb-6">
            <Loader2 className="h-8 w-8 text-brand-purple animate-spin" />
          </div>
          <h3 className="text-lg font-medium mb-2">Analyzing Your Video</h3>
          <p className="text-muted-foreground max-w-[350px]">
            Finding the perfect tags for maximum discoverability...
          </p>
        </div>
      ) : (
        <div className="flex flex-col items-center text-center space-y-3">
          <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center mb-2">
            <Tag className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium">No Tags Generated Yet</h3>
          <p className="text-sm text-muted-foreground max-w-[350px]">
            Complete the form and click "Generate Tags" to get optimized tag recommendations that will help your video reach more viewers.
          </p>
        </div>
      )}
    </div>
  );
};

export default GeneratedTagsPlaceholder;
