import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Home,
  Video,
  Image,
  FileText,
  Tag,
  BarChart,
  Settings,
  Youtube,
  Menu,
  X,
  HelpCircle,
  Plus,
  Library,
  TrendingUp,
  Lightbulb
} from "lucide-react";
import { Button } from "@/components/ui/button";

const Sidebar = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Updated navigation links with categories
  const dashboardLinks = [
    { label: "Dashboard", icon: Home, path: "/dashboard" },
    { label: "My Videos", icon: Library, path: "/my-videos" },
    { label: "New Video", icon: Plus, path: "/new-video" },
  ];
    const aiToolsLinks = [
    { label: "Title Generator", icon: Video, path: "/title-generator" },
    { label: "Thumbnail Creator", icon: Image, path: "/thumbnail-creator" },
    { label: "Description Writer", icon: FileText, path: "/description-writer" },
    { label: "Tag Optimizer", icon: Tag, path: "/tag-optimizer", disabled: true },
  ];
  
  const insightsLinks = [
    { label: "Analytics", icon: BarChart, path: "/analytics", disabled: true },
    { label: "Trends", icon: TrendingUp, path: "/trends", disabled: true },
    { label: "Video Ideas", icon: Lightbulb, path: "/video-ideas", disabled: true },
  ];
  
  const settingsLinks = [
    { label: "Settings", icon: Settings, path: "/settings", disabled: true },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const renderLinks = (links: any[]) => {
    return links.map((link) => (
      <Link
        key={link.path}
        to={link.disabled ? "#" : link.path}
        className={cn(
          "flex items-center gap-3 rounded-md px-3 py-2.5 text-sm font-medium transition-colors",
          location.pathname === link.path
            ? "bg-gradient-to-r from-brand-purple to-brand-blue text-white shadow-md"
            : "hover:bg-gradient-to-r hover:from-brand-purple/5 hover:to-brand-blue/5",
          link.disabled ? "opacity-50 cursor-not-allowed pointer-events-none" : ""
        )}
        onClick={(e) => link.disabled && e.preventDefault()}
      >
        <link.icon className={cn("h-4 w-4", location.pathname === link.path ? "text-white" : "text-brand-purple")} />
        {link.label}
        {link.disabled && <span className="ml-auto text-xs bg-gray-100 dark:bg-gray-800 rounded-full px-2 py-0.5">Soon</span>}
      </Link>
    ));
  };

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-brand-purple/10 to-brand-blue/10 border-b shadow-sm flex items-center justify-between px-4 h-16">
        <Link to="/" className="flex items-center gap-2">
          <Youtube className="h-5 w-5 text-brand-purple" />
          <span className="font-bold gradient-text">VideoGrowth AI</span>
        </Link>
        <Button variant="ghost" size="icon" onClick={toggleMobileMenu}>
          {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>
      </div>

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "h-screen sticky top-0 z-40 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 border-r shadow-sm transition-transform lg:translate-x-0 lg:w-64 flex-shrink-0",
          isMobileMenuOpen ? "translate-x-0 fixed inset-y-0 left-0 w-64" : "-translate-x-full fixed inset-y-0 left-0 w-64"
        )}
      >
        <div className="flex h-16 items-center gap-2 px-6 border-b bg-gradient-to-r from-brand-purple/10 to-brand-blue/10">
          <Link to="/" className="flex items-center gap-2">
            <Youtube className="h-5 w-5 text-brand-purple" />
            <span className="font-bold gradient-text">VideoGrowth AI</span>
          </Link>
        </div>

        <div className="flex flex-col gap-1 p-3 flex-grow overflow-y-auto">
          {/* Dashboard Links */}
          <div className="mb-2">
            <div className="px-3 py-2">
              <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                Main
              </h3>
            </div>
            {renderLinks(dashboardLinks)}
          </div>

          {/* AI Tools Links */}
          <div className="mb-2">
            <div className="px-3 py-2">
              <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                AI Tools
              </h3>
            </div>
            {renderLinks(aiToolsLinks)}
          </div>

          {/* Insights Links */}
          <div className="mb-2">
            <div className="px-3 py-2">
              <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                Insights
              </h3>
            </div>
            {renderLinks(insightsLinks)}
          </div>

          {/* Settings Links */}
          <div>
            <div className="px-3 py-2">
              <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                Settings
              </h3>
            </div>
            {renderLinks(settingsLinks)}
          </div>
        </div>

        <div className="p-4 mt-auto border-t border-gray-100 dark:border-gray-800">
          <div className="rounded-lg bg-gradient-to-r from-brand-purple/10 to-brand-blue/10 p-4 border border-brand-purple/20">
            <div className="flex items-center gap-2 mb-2">
              <HelpCircle className="h-4 w-4 text-brand-purple" />
              <h4 className="text-sm font-medium">Need help?</h4>
            </div>
            <p className="text-xs text-muted-foreground mb-3">
              Check our documentation or contact support for assistance.
            </p>
            <Button variant="secondary" size="sm" className="w-full bg-white hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700">
              Contact Support
            </Button>
          </div>
        </div>
      </aside>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 backdrop-blur-sm lg:hidden"
          onClick={toggleMobileMenu}
        />
      )}
    </>
  );
};

export default Sidebar;
