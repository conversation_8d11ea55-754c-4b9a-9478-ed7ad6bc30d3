import React from "react";

interface ErrorDisplayProps {
  error: string | null;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => {
  if (!error) return null;
  
  return (
    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm">
      <p className="font-medium mb-1">Error</p>
      <p>{error}</p>
      <p className="mt-2 text-xs">
        If the issue persists, check your API keys or try again later.
      </p>
    </div>
  );
};

export default ErrorDisplay;
