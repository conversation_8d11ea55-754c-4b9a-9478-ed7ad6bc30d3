import React from "react";
import { Tag, ThumbsUp } from "lucide-react";

// Import local components - use relative paths for better resolution
import TagOptimizerInputs from "./TagOptimizerInputs";
import TagOptimizerActions from "./TagOptimizerActions";
import TagSuggestions from "./TagSuggestions";
import GeneratedTagsPlaceholder from "./GeneratedTagsPlaceholder";

interface VideoDetails {
  title: string;
  description: string;
}

interface TagOptimizerContainerProps {
  videoDetails: VideoDetails;
  isLoading: boolean;
  generatedTags: string[];
  onGenerateTags: (data: any) => void;
  onResetTags: () => void;
}

const TagOptimizerContainer: React.FC<TagOptimizerContainerProps> = ({
  videoDetails,
  isLoading,
  generatedTags,
  onGenerateTags,
  onResetTags,
}) => {  return (
    <div className="space-y-6">
      {/* Input and Tag Generation Section */}
      <div>
        {/* Tag Generator Section */}
        <div className="mb-6 rounded-xl overflow-hidden border border-border/30 bg-background/40 backdrop-blur-lg shadow-sm">
          <div className="bg-muted/50 p-4 border-b border-border/30">
            <h2 className="flex items-center gap-2 text-xl font-medium">
              <Tag className="h-5 w-5 text-brand-purple" />
              Tag Generator
            </h2>
          </div>
          
          <div className="p-6">
            <TagOptimizerInputs
              initialValues={videoDetails}
              isLoading={isLoading}
              onSubmit={onGenerateTags}
            />
          </div>
        </div>
        
        {/* Generated Tags Section */}
        <div className="rounded-xl overflow-hidden border border-border/30 bg-background/40 backdrop-blur-lg shadow-sm">
          <div className="bg-muted/50 p-4 border-b border-border/30">
            <h2 className="flex items-center gap-2 text-xl font-medium">
              {generatedTags.length > 0 ? (
                <ThumbsUp className="h-5 w-5 text-brand-blue" />
              ) : (
                <Tag className="h-5 w-5 text-brand-blue" />
              )}
              Generated Tags
            </h2>
          </div>
          
          <div className="p-6">
            {generatedTags.length > 0 ? (
              <TagSuggestions tags={generatedTags} />
            ) : (
              <GeneratedTagsPlaceholder isLoading={isLoading} />
            )}
          </div>
        </div>
      </div>
      
      {/* Actions Section */}
      {generatedTags.length > 0 && (
        <TagOptimizerActions onReset={onResetTags} />
      )}
    </div>
  );
};

export default TagOptimizerContainer;
