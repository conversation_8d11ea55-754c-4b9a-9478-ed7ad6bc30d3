import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw, Save, FileEdit, Youtube } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface TagOptimizerActionsProps {
  onReset: () => void;
}

const TagOptimizerActions: React.FC<TagOptimizerActionsProps> = ({ onReset }) => {
  const handleSaveTags = () => {
    // This would typically save tags to the video in your application
    toast({
      title: "Tags saved",
      description: "Your tags have been saved successfully",
    });
  };

  const handleApplyToYoutube = () => {
    toast({
      title: "Feature coming soon",
      description: "Direct YouTube integration will be available soon!",
    });
  };
  return (
    <div className="rounded-lg border border-border/30 bg-background/40 backdrop-blur-lg mt-4">
      <div className="p-4 flex flex-wrap items-center gap-3 justify-between">
        <div className="text-sm text-muted-foreground hidden sm:block">
          Ready to use these tags for your video?
        </div>
        
        <div className="flex flex-wrap gap-3 ml-auto">
          <Button 
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={onReset}
          >
            <RefreshCw className="h-4 w-4" />
            Generate New Tags
          </Button>
          
          <Button 
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={handleSaveTags}
          >
            <Save className="h-4 w-4" />
            Save Tags
          </Button>
          
          <Button 
            size="sm"
            className="flex items-center gap-2 bg-brand-purple hover:bg-brand-purple/90"
            onClick={handleApplyToYoutube}
          >
            <Youtube className="h-4 w-4" />
            Apply to YouTube
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TagOptimizerActions;
