
import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Youtube } from "lucide-react";
import Navbar from "@/components/landing/Navbar";
import FeatureCard from "@/components/landing/FeatureCard";
import Footer from "@/components/landing/Footer";
import TestimonialCard from "@/components/landing/TestimonialCard";
import HeroImage from "@/components/landing/HeroImage";
import HomeVideoForm from "@/components/home/<USER>";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-brand-purple/10 to-brand-blue/10 py-20 md:py-32">
        <div className="container px-4 md:px-6">
          <div className="grid gap-12 md:grid-cols-2 md:gap-16 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tighter">
                Grow Your <span className="gradient-text">YouTube Channel</span> with AI
              </h1>
              <p className="mt-4 text-muted-foreground text-lg md:text-xl max-w-[600px]">
                Skyrocket your YouTube success with our AI-powered tools. Generate high-converting titles, thumbnails, descriptions, and metadata that drive views and subscribers.
              </p>
              <div className="mt-8 flex flex-wrap gap-4">
                <Link to="/dashboard">
                  <Button size="lg" className="bg-gradient-to-r from-brand-purple to-brand-blue hover:opacity-90">
                    Get Started <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Button variant="outline" size="lg">
                  Learn More
                </Button>
              </div>

              <div className="mt-8 flex items-center gap-4 text-sm">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="h-8 w-8 rounded-full bg-gray-300 border-2 border-white" />
                  ))}
                </div>
                <p className="font-medium">
                  <span className="font-semibold">1,000+</span> creators already using our tools
                </p>
              </div>
            </div>
            <div className="relative">
              <HeroImage />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white" id="features">
        <div className="container px-4 md:px-6">
          <div className="text-center max-w-[800px] mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              AI Tools to <span className="gradient-text">Supercharge</span> Your Channel
            </h2>
            <p className="text-muted-foreground text-lg">
              Our suite of AI-powered tools helps you create content that ranks higher, gets more views, and grows your audience faster.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <FeatureCard
              title="AI Title Generator"
              description="Create high-CTR titles that grab attention and drive clicks with our advanced AI."
              icon="video"
              linkText="Generate Titles"
              linkUrl="/title-generator"
            />
            <FeatureCard
              title="Thumbnail Creator"
              description="Design eye-catching thumbnails that stand out in search results and suggested videos."
              icon="image"
              linkText="Design Thumbnails"
              linkUrl="/thumbnail-creator"
            />
            <FeatureCard
              title="Description Writer"
              description="Generate SEO-optimized descriptions that help your videos rank higher in search."
              icon="file-text"
              linkText="Write Descriptions"
              linkUrl="/dashboard"
              disabled
            />
            <FeatureCard
              title="Tag Optimizer"
              description="Find the perfect tags to improve discoverability and reach more viewers."
              icon="tag"
              linkText="Optimize Tags"
              linkUrl="/dashboard"
              disabled
            />
            <FeatureCard
              title="Analytics Dashboard"
              description="Track your progress with detailed analytics and actionable insights."
              icon="bar-chart"
              linkText="View Analytics"
              linkUrl="/dashboard"
              disabled
            />
            <FeatureCard
              title="Competitor Analysis"
              description="See what's working for successful channels in your niche and adapt your strategy."
              icon="search"
              linkText="Analyze Competitors"
              linkUrl="/dashboard"
              disabled
            />
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-muted/50">
        <div className="container px-4 md:px-6">
          <div className="text-center max-w-[800px] mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              How It <span className="gradient-text">Works</span>
            </h2>
            <p className="text-muted-foreground text-lg">
              Get started in minutes and transform your YouTube content creation process.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            <div className="bg-white p-8 rounded-lg shadow-sm text-center">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-primary font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Upload Your Video</h3>
              <p className="text-muted-foreground">
                Upload your video or provide a link to analyze its content automatically.
              </p>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-sm text-center">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-primary font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Choose AI Tools</h3>
              <p className="text-muted-foreground">
                Select which elements you want to optimize: title, thumbnail, description, or tags.
              </p>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-sm text-center">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-primary font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Get AI Recommendations</h3>
              <p className="text-muted-foreground">
                Review AI-generated options and select the ones that best fit your content and style.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-white">
        <div className="container px-4 md:px-6">
          <div className="text-center max-w-[800px] mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              What <span className="gradient-text">Creators</span> Say
            </h2>
            <p className="text-muted-foreground text-lg">
              Join thousands of content creators who have transformed their YouTube strategy.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <TestimonialCard
              quote="This tool helped me double my CTR in just one month. The AI title suggestions are spot-on!"
              author="Alex Morgan"
              role="Tech Reviewer"
              subscribers="250K subscribers"
            />
            <TestimonialCard
              quote="I was struggling to come up with engaging titles, but now I have multiple high-performing options for every video."
              author="Sarah Johnson"
              role="Lifestyle Vlogger"
              subscribers="500K subscribers"
            />
            <TestimonialCard
              quote="The thumbnail suggestions perfectly capture the essence of my videos and have significantly improved my click-through rate."
              author="Michael Chen"
              role="Gaming Creator"
              subscribers="1.2M subscribers"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand-purple to-brand-blue text-white">
        <div className="container px-4 md:px-6 text-center">
          <Youtube className="h-16 w-16 mx-auto mb-6 opacity-90" />
          <h2 className="text-3xl md:text-4xl font-bold mb-4 max-w-[800px] mx-auto">
            Ready to Take Your YouTube Channel to the Next Level?
          </h2>
          <p className="text-lg md:text-xl text-white/80 mb-8 max-w-[600px] mx-auto">
            Join thousands of creators who are using AI to grow faster and work smarter.
          </p>
          <Link to="/dashboard">
            <Button size="lg" variant="secondary" className="font-semibold">
              Start Growing Your Channel <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
